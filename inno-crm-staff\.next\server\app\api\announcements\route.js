"use strict";(()=>{var e={};e.id=9610,e.ids=[9610],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91764:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>N,routeModule:()=>f,serverHooks:()=>q,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>h});var n={};t.r(n),t.d(n,{GET:()=>A,POST:()=>g});var s=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(19854),p=t(41098),c=t(79464),d=t(99326),l=t(96330),x=t(45697);let m=x.Ik({title:x.Yj().min(1,"Title is required"),content:x.Yj().min(1,"Content is required"),priority:x.k5(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),targetAudience:x.k5(["STUDENTS","TEACHERS","ACADEMIC_MANAGERS","ALL"]).default("ALL"),isActive:x.zM().default(!0),expiresAt:x.Yj().optional()});async function A(e){try{let r=await (0,u.getServerSession)(p.N);if(!r?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),n=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"20"),o=t.get("priority"),i=t.get("targetAudience"),d=(n-1)*s,l={};o&&"ALL"!==o&&(l.priority=o),i&&"ALL"!==i&&(l.targetAudience=i);let[x,m]=await Promise.all([c.z.announcement.findMany({where:l,include:{author:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:d,take:s}),c.z.announcement.count({where:l})]);return a.NextResponse.json({announcements:x,pagination:{page:n,limit:s,total:m,pages:Math.ceil(m/s)}})}catch(e){return console.error("Error fetching announcements:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let r=await (0,u.getServerSession)(p.N);if(!r?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.role||!["ADMIN","MANAGER"].includes(r.user.role))return a.NextResponse.json({error:"Forbidden"},{status:403});let t=await e.json(),n=m.parse(t),s=await c.z.announcement.create({data:{title:n.title,content:n.content,priority:n.priority,targetAudience:n.targetAudience,isActive:n.isActive,authorId:r.user.id},include:{author:{select:{name:!0,email:!0}}}});return await d._.log({userId:r.user.id,userRole:r.user.role||l.Role.ADMIN,action:"CREATE",resource:"ANNOUNCEMENT",resourceId:s.id,details:`Created announcement: ${n.title}`}),a.NextResponse.json(s,{status:201})}catch(e){if(e instanceof x.G)return a.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating announcement:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/announcements/route",pathname:"/api/announcements",filename:"route",bundlePath:"app/api/announcements/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\announcements\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:v,workUnitAsyncStorage:h,serverHooks:q}=f;function N(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:h})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4243,580,5697,3412,1971],()=>t(91764));module.exports=n})();