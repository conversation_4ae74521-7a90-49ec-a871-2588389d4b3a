(()=>{var e={};e.id=4680,e.ids=[4680],e.modules={2553:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>d});var s=t(60687),n=t(43210),a=t(8730),o=t(24224),i=t(96241);let d=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...o},l)=>{let c=n?a.DX:"button";return(0,s.jsx)(c,{className:(0,i.cn)(d({variant:r,size:t,className:e})),ref:l,...o})});l.displayName="Button"},3018:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>d,TN:()=>l});var s=t(60687),n=t(43210),a=t(24224),o=t(96241);let i=(0,a.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=n.forwardRef(({className:e,variant:r,...t},n)=>(0,s.jsx)("div",{ref:n,role:"alert",className:(0,o.cn)(i({variant:r}),e),...t}));d.displayName="Alert",n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",e),...r})).displayName="AlertTitle";let l=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",e),...r}));l.displayName="AlertDescription"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4432:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>a});var s=t(60687),n=t(82136);function a({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},5106:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\providers\\auth-provider.tsx","AuthProvider")},5161:(e,r,t)=>{Promise.resolve().then(t.bind(t,5106)),Promise.resolve().then(t.bind(t,13910))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(18962).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(18962).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13910:(e,r,t)=>{"use strict";t.d(r,{QueryProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\providers\\query-provider.tsx","QueryProvider")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},18962:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(43210),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(e,r)=>{let t=(0,s.forwardRef)(({color:t="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:l="",children:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...n,width:o,height:o,stroke:t,strokeWidth:d?24*Number(i)/Number(o):i,className:["lucide",`lucide-${a(e)}`,l].join(" "),...u},[...r.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(c)?c:[c]]));return t.displayName=`${e}`,t}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30420:(e,r,t)=>{Promise.resolve().then(t.bind(t,54259))},33873:e=>{"use strict";e.exports=require("path")},39107:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},39390:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(60687),n=t(43210),a=t(78148),o=t(24224),i=t(96241);let d=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)(a.b,{ref:t,className:(0,i.cn)(d(),e),...r}));l.displayName=a.b.displayName},48340:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(18962).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},50391:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),n=t(48088),a=t(88170),o=t.n(a),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54259)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\auth\\signin\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\auth\\signin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},54259:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-staff\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\auth\\signin\\page.tsx","default")},55192:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>o,aR:()=>i});var s=t(60687),n=t(43210),a=t(96241);let o=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...r}));o.displayName="Card";let i=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let d=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},58014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>d});var s=t(37413),n=t(61421),a=t.n(n),o=t(5106),i=t(13910);t(82704);let d={title:"Innovative Centre CRM",description:"Customer Relationship Management System for Innovative Centre"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsx)(i.QueryProvider,{children:(0,s.jsx)(o.AuthProvider,{children:e})})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63851:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(18962).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},66804:(e,r,t)=>{Promise.resolve().then(t.bind(t,80547))},68988:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(60687),n=t(43210),a=t(96241);let o=n.forwardRef(({className:e,type:r,...t},n)=>(0,s.jsx)("input",{type:r,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));o.displayName="Input"},70313:(e,r,t)=>{Promise.resolve().then(t.bind(t,4432)),Promise.resolve().then(t.bind(t,80924))},72730:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},79435:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},80547:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>A});var s=t(60687),n=t(43210),a=t(82136),o=t(16189),i=t(27605),d=t(63442),l=t(9275),c=t(85814),u=t.n(c),m=t(2553),h=t(68988),p=t(39390),f=t(55192),x=t(3018),v=t(82080),b=t(63851),g=t(48340);let y=(0,t(18962).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var w=t(12597),j=t(13861),N=t(72730);let P=l.Ik({phone:l.Yj().min(9,"Please enter a valid phone number"),password:l.Yj().min(6,"Password must be at least 6 characters")});function k(){let[e,r]=(0,n.useState)(!1),[t,l]=(0,n.useState)(""),[c,k]=(0,n.useState)(!1),A=(0,o.useRouter)(),C=(0,o.useSearchParams)(),R=C.get("callbackUrl")||"/dashboard";C.get("error");let{register:S,handleSubmit:E,formState:{errors:D}}=(0,i.mN)({resolver:(0,d.u)(P)}),I=async e=>{r(!0),l("");try{let r=await (0,a.signIn)("credentials",{phone:e.phone,password:e.password,redirect:!1});if(r?.error)l("Invalid phone number or password");else{let e=await (0,a.getSession)();if(e?.user)switch(e.user.role){case"STUDENT":A.push("/dashboard/student");break;case"TEACHER":A.push("/dashboard/teacher");break;case"RECEPTION":A.push("/dashboard/leads");break;case"CASHIER":A.push("/dashboard/payments");break;case"ACADEMIC_MANAGER":A.push("/dashboard/assessments");break;default:A.push(R)}}}catch(e){l("An error occurred. Please try again.")}finally{r(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsxs)(u(),{href:"/",className:"inline-flex items-center",children:[(0,s.jsx)(v.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Innovative Centre"})]})}),(0,s.jsxs)(f.Zp,{children:[(0,s.jsxs)(f.aR,{children:[(0,s.jsx)(f.ZB,{children:"Staff Login"}),(0,s.jsx)(f.BT,{children:"Enter your credentials to access the CRM system"})]}),(0,s.jsxs)(f.Wu,{children:[(0,s.jsxs)("form",{onSubmit:E(I),className:"space-y-4",children:[t&&(0,s.jsxs)(x.Fc,{variant:"destructive",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),(0,s.jsx)(x.TN,{children:t})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p.J,{htmlFor:"phone",children:"Phone Number"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(h.p,{id:"phone",...S("phone"),placeholder:"+998 XX XXX XX XX",className:"pl-10",disabled:e})]}),D.phone&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:D.phone.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(p.J,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(h.p,{id:"password",type:c?"text":"password",...S("password"),placeholder:"Enter your password",className:"pl-10 pr-10",disabled:e}),(0,s.jsx)("button",{type:"button",onClick:()=>k(!c),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:e,children:c?(0,s.jsx)(w.A,{className:"h-4 w-4"}):(0,s.jsx)(j.A,{className:"h-4 w-4"})})]}),D.password&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:D.password.message})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{id:"remember",type:"checkbox",className:"rounded border-gray-300"}),(0,s.jsx)(p.J,{htmlFor:"remember",className:"text-sm",children:"Remember me"})]}),(0,s.jsx)(u(),{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot password?"})]}),(0,s.jsx)(m.$,{type:"submit",className:"w-full",disabled:e,children:e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(N.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(u(),{href:"/contact",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Contact administrator"})]})}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)(u(),{href:"/",className:"text-sm text-blue-600 hover:underline",children:"← Back to Homepage"})})]})]}),(0,s.jsxs)("div",{className:"text-center mt-8 text-sm text-gray-500",children:[(0,s.jsx)("p",{children:"\xa9 2024 Innovative Centre. All rights reserved."}),(0,s.jsxs)("p",{className:"mt-1",children:["Need help?"," ",(0,s.jsx)(u(),{href:"/contact",className:"text-blue-600 hover:text-blue-500",children:"Contact Support"})]})]})]})})}function A(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(k,{})})}},80924:(e,r,t)=>{"use strict";t.d(r,{QueryProvider:()=>i});var s=t(60687),n=t(92314),a=t(8693),o=t(43210);function i({children:e}){let[r]=(0,o.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:6e4}}}));return(0,s.jsx)(a.Ht,{client:r,children:e})}},82080:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(18962).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},82704:()=>{},96241:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>i,cn:()=>a,r6:()=>d,vv:()=>o});var s=t(49384),n=t(82348);function a(...e){return(0,n.QP)((0,s.$)(e))}function o(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(r)}function d(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,7615,2918,7825],()=>t(50391));module.exports=s})();