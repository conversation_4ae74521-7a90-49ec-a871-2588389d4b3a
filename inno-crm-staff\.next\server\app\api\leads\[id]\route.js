"use strict";(()=>{var e={};e.id=9479,e.ids=[9479],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")},97469:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>A,serverHooks:()=>E,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>j});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>w,PUT:()=>g});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),u=r(19854),d=r(41098),l=r(79464),p=r(99326),c=r(45697);let x=c.Ik({status:c.k5(["NEW","CALLING","CALL_COMPLETED","GROUP_ASSIGNED","ARCHIVED","NOT_INTERESTED"]).optional(),notes:c.Yj().optional(),assignedTo:c.Yj().optional(),callStartedAt:c.Yj().optional(),callEndedAt:c.Yj().optional(),callDuration:c.ai().optional(),assignedGroupId:c.Yj().optional(),assignedTeacherId:c.Yj().optional()});async function w(e,{params:t}){try{let{id:e}=await t,r=await l.z.lead.findUnique({where:{id:e}});if(!r)return i.NextResponse.json({error:"Lead not found"},{status:404});return i.NextResponse.json(r)}catch(e){return console.error("Error fetching lead:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e,{params:t}){try{let{id:r}=await t,s=await (0,u.getServerSession)(d.N);if(!s?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let a=await e.json(),n=x.parse(a),o=await l.z.lead.findUnique({where:{id:r}});if(!o)return i.NextResponse.json({error:"Lead not found"},{status:404});let c={...n,updatedAt:new Date};n.callStartedAt&&(c.callStartedAt=new Date(n.callStartedAt)),n.callEndedAt&&(c.callEndedAt=new Date(n.callEndedAt)),"ARCHIVED"===n.status&&"ARCHIVED"!==o.status&&(c.archivedAt=new Date),n.assignedGroupId&&!o.assignedGroupId&&(c.assignedAt=new Date);let w=await l.z.lead.update({where:{id:r},data:c,include:{assignedGroup:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},assignedTeacher:{include:{user:{select:{name:!0}}}}}});return n.status&&n.status!==o.status&&("CALLING"===n.status?await p._.logLeadContacted(s.user.id,s.user.role,w.id,{leadName:w.name,leadPhone:w.phone,previousStatus:o.status,newStatus:w.status,notes:"Call started"},e):"GROUP_ASSIGNED"===n.status&&await p._.logLeadContacted(s.user.id,s.user.role,w.id,{leadName:w.name,leadPhone:w.phone,previousStatus:o.status,newStatus:w.status,notes:`Assigned to group: ${w.assignedGroup?.name||"Unknown"}`},e)),i.NextResponse.json(w)}catch(e){if(e instanceof c.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating lead:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:t}){try{let{id:e}=await t;return await l.z.lead.delete({where:{id:e}}),i.NextResponse.json({message:"Lead deleted successfully"})}catch(e){return console.error("Error deleting lead:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let A=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/leads/[id]/route",pathname:"/api/leads/[id]",filename:"route",bundlePath:"app/api/leads/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:j,serverHooks:E}=A;function v(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:j})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,5697,3412,1971],()=>r(97469));module.exports=s})();