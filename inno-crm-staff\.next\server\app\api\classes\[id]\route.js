"use strict";(()=>{var e={};e.id=2832,e.ids=[2832],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66391:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>m,serverHooks:()=>N,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>j});var s={};t.r(s),t.d(s,{DELETE:()=>f,GET:()=>g,PUT:()=>x});var n=t(96559),o=t(48088),a=t(37719),u=t(32190),i=t(19854),d=t(41098),c=t(79464),l=t(99326),p=t(45697);let h=p.Ik({groupId:p.Yj().optional(),teacherId:p.Yj().optional(),date:p.Yj().optional(),topic:p.Yj().optional(),homework:p.Yj().optional(),notes:p.Yj().optional()});async function g(e,{params:r}){try{let e=await (0,i.getServerSession)(d.N);if(!e?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r,s=await c.z.class.findUnique({where:{id:t},include:{group:{include:{course:{select:{id:!0,name:!0,level:!0,description:!0}},enrollments:{include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0}}}}},where:{status:"ACTIVE"}}}},teacher:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}},attendances:{include:{student:{include:{user:{select:{id:!0,name:!0}}}}},orderBy:{createdAt:"desc"}}}});if(!s)return u.NextResponse.json({error:"Class not found"},{status:404});return u.NextResponse.json(s)}catch(e){return console.error("Error fetching class:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let t=await (0,i.getServerSession)(d.N);if(!t?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER","TEACHER"].includes(t.user.role))return u.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,n=await e.json(),o=h.parse(n),a=await c.z.class.findUnique({where:{id:s},include:{group:{select:{name:!0}},teacher:{include:{user:{select:{name:!0}}}}}});if(!a)return u.NextResponse.json({error:"Class not found"},{status:404});if(o.groupId&&o.groupId!==a.groupId&&!await c.z.group.findUnique({where:{id:o.groupId}}))return u.NextResponse.json({error:"Group not found"},{status:400});if(o.teacherId&&o.teacherId!==a.teacherId&&!await c.z.teacher.findUnique({where:{id:o.teacherId}}))return u.NextResponse.json({error:"Teacher not found"},{status:400});if(o.date){let e=new Date(o.date);if(await c.z.class.findFirst({where:{id:{not:s},groupId:o.groupId||a.groupId,date:e}}))return u.NextResponse.json({error:"A class already exists for this group on this date"},{status:400})}let p={...o};o.date&&(p.date=new Date(o.date));let g=await c.z.class.update({where:{id:s},data:p,include:{group:{include:{course:{select:{name:!0,level:!0}}}},teacher:{include:{user:{select:{id:!0,name:!0}}}},_count:{select:{attendances:!0}}}});return await l._.log({userId:t.user.id,userRole:t.user.role,action:"UPDATE",resource:"class",resourceId:g.id,details:{changes:o,groupName:g.group.name,courseName:g.group.course?.name,teacherName:g.teacher.user.name},ipAddress:l._.getIpAddress(e),userAgent:l._.getUserAgent(e)}),u.NextResponse.json(g)}catch(e){if(e instanceof p.G)return u.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating class:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e,{params:r}){try{let t=await (0,i.getServerSession)(d.N);if(!t?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER"].includes(t.user.role))return u.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,n=await c.z.class.findUnique({where:{id:s},include:{group:{select:{name:!0}},teacher:{include:{user:{select:{name:!0}}}},_count:{select:{attendances:!0}}}});if(!n)return u.NextResponse.json({error:"Class not found"},{status:404});if(n._count.attendances>0)return u.NextResponse.json({error:"Cannot delete class with attendance records",details:"This class has attendance records. Please delete attendance records first or contact an administrator."},{status:400});return await c.z.class.delete({where:{id:s}}),await l._.log({userId:t.user.id,userRole:t.user.role,action:"DELETE",resource:"class",resourceId:s,details:{groupName:n.group.name,teacherName:n.teacher.user.name,date:n.date.toISOString(),topic:n.topic},ipAddress:l._.getIpAddress(e),userAgent:l._.getUserAgent(e)}),u.NextResponse.json({message:"Class deleted successfully"})}catch(e){return console.error("Error deleting class:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/classes/[id]/route",pathname:"/api/classes/[id]",filename:"route",bundlePath:"app/api/classes/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\classes\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:j,serverHooks:N}=m;function R(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:j})}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(66391));module.exports=s})();