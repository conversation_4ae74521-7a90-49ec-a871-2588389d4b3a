"use strict";exports.id=8887,exports.ids=[8887],exports.modules={43:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(43210);n(60687);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(43210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},9510:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(43210),o=n(11273),i=n(98599),a=n(8730),l=n(60687);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.A)(t),[s,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,l.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,a.TL)(f),h=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=c(f,n),a=(0,i.s)(t,o.collectionRef);return(0,l.jsx)(p,{ref:a,children:r})});h.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",y=(0,a.TL)(m),g=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,u=r.useRef(null),s=(0,i.s)(t,u),d=c(m,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...a}),()=>void d.itemMap.delete(u))),(0,l.jsx)(y,{...{[v]:""},ref:s,children:o})});return g.displayName=m,[{Provider:d,Slot:h,ItemSlot:g},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}var s=new WeakMap;function c(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=d(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},10022:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},11273:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(43210),o=n(60687);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[l]||a,c=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12187:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]])},13495:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(43210);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15814:(e,t,n)=>{n.d(t,{A:()=>g});var r={};function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){if(t.length<e)throw TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function a(e){i(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"===o(e)&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):(("string"==typeof e||"[object String]"===t)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))}function l(e,t){i(2,arguments);var n=a(e),r=a(t),o=n.getTime()-r.getTime();return o<0?-1:o>0?1:o}var u={ceil:Math.ceil,round:Math.round,floor:Math.floor,trunc:function(e){return e<0?Math.ceil(e):Math.floor(e)}},s={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function c(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}var d={date:c({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:c({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:c({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},f={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function p(e){return function(t,n){var r;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&e.formattingValues){var o=e.defaultFormattingWidth||e.defaultWidth,i=null!=n&&n.width?String(n.width):o;r=e.formattingValues[i]||e.formattingValues[o]}else{var a=e.defaultWidth,l=null!=n&&n.width?String(n.width):e.defaultWidth;r=e.values[l]||e.values[a]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function h(e){return function(t){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.width,i=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],a=t.match(i);if(!a)return null;var l=a[0],u=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],s=Array.isArray(u)?function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}(u,function(e){return e.test(l)}):function(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}(u,function(e){return e.test(l)});return n=e.valueCallback?e.valueCallback(s):s,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(l.length)}}}let m={code:"en-US",formatDistance:function(e,t,n){var r,o=s[e];if(r="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),null!=n&&n.addSuffix)if(n.comparison&&n.comparison>0)return"in "+r;else return r+" ago";return r},formatLong:d,formatRelative:function(e,t,n,r){return f[e]},localize:{ordinalNumber:function(e,t){var n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:p({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:p({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:p({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:p({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:p({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.match(e.matchPattern);if(!r)return null;var o=r[0],i=t.match(e.parsePattern);if(!i)return null;var a=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:a=n.valueCallback?n.valueCallback(a):a,rest:t.slice(o.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:h({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:h({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:h({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:h({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:h({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function v(e,t){if(null==e)throw TypeError("assign requires that input parameter not be null or undefined");for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}function y(e){var t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),e.getTime()-t.getTime()}function g(e,t){return i(1,arguments),function(e,t,n){i(2,arguments);var o,s,c,d,f,p=null!=(o=null!=(s=null==n?void 0:n.locale)?s:r.locale)?o:m;if(!p.formatDistance)throw RangeError("locale must contain formatDistance property");var h=l(e,t);if(isNaN(h))throw RangeError("Invalid time value");var g=v(v({},n),{addSuffix:!!(null==n?void 0:n.addSuffix),comparison:h});h>0?(c=a(t),d=a(e)):(c=a(e),d=a(t));var w=function(e,t,n){i(2,arguments);var r,o=function(e,t){return i(2,arguments),a(e).getTime()-a(t).getTime()}(e,t)/1e3;return((r=null==n?void 0:n.roundingMethod)?u[r]:u.trunc)(o)}(d,c),b=Math.round((w-(y(d)-y(c))/1e3)/60);if(b<2)if(null!=n&&n.includeSeconds)if(w<5)return p.formatDistance("lessThanXSeconds",5,g);else if(w<10)return p.formatDistance("lessThanXSeconds",10,g);else if(w<20)return p.formatDistance("lessThanXSeconds",20,g);else if(w<40)return p.formatDistance("halfAMinute",0,g);else if(w<60)return p.formatDistance("lessThanXMinutes",1,g);else return p.formatDistance("xMinutes",1,g);else if(0===b)return p.formatDistance("lessThanXMinutes",1,g);else return p.formatDistance("xMinutes",b,g);if(b<45)return p.formatDistance("xMinutes",b,g);if(b<90)return p.formatDistance("aboutXHours",1,g);if(b<1440){var x=Math.round(b/60);return p.formatDistance("aboutXHours",x,g)}if(b<2520)return p.formatDistance("xDays",1,g);else if(b<43200){var E=Math.round(b/1440);return p.formatDistance("xDays",E,g)}else if(b<86400)return f=Math.round(b/43200),p.formatDistance("aboutXMonths",f,g);if((f=function(e,t){i(2,arguments);var n,r=a(e),o=a(t),u=l(r,o),s=Math.abs(function(e,t){i(2,arguments);var n=a(e),r=a(t);return 12*(n.getFullYear()-r.getFullYear())+(n.getMonth()-r.getMonth())}(r,o));if(s<1)n=0;else{1===r.getMonth()&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-u*s);var c=l(r,o)===-u;(function(e){i(1,arguments);var t=a(e);return(function(e){i(1,arguments);var t=a(e);return t.setHours(23,59,59,999),t})(t).getTime()===(function(e){i(1,arguments);var t=a(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t})(t).getTime()})(a(e))&&1===s&&1===l(e,o)&&(c=!1),n=u*(s-Number(c))}return 0===n?0:n}(d,c))<12){var C=Math.round(b/43200);return p.formatDistance("xMonths",C,g)}var M=f%12,S=Math.floor(f/12);return M<3?p.formatDistance("aboutXYears",S,g):M<9?p.formatDistance("overXYears",S,g):p.formatDistance("almostXYears",S+1,g)}(e,Date.now(),t)}},16189:(e,t,n)=>{var r=n(65773);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},17313:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},18853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=n(66156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},18962:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(43210),o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),a=(e,t)=>{let n=(0,r.forwardRef)(({color:n="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:u,className:s="",children:c,...d},f)=>(0,r.createElement)("svg",{ref:f,...o,width:a,height:a,stroke:n,strokeWidth:u?24*Number(l)/Number(a):l,className:["lucide",`lucide-${i(e)}`,s].join(" "),...d},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return n.displayName=`${e}`,n}},23026:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},23689:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(43210),o=n(51215),i=n(14163),a=n(66156),l=n(60687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[s,c]=r.useState(!1);(0,a.N)(()=>c(!0),[]);let d=n||s&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(i.sG.div,{...u,ref:t}),d):null});u.displayName="Portal"},25541:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},26312:(e,t,n)=>{n.d(t,{H_:()=>e8,UC:()=>e6,YJ:()=>e3,q7:()=>e9,VF:()=>te,JU:()=>e4,ZL:()=>e2,z6:()=>e5,hN:()=>e7,bL:()=>e0,wv:()=>tt,Pb:()=>tn,G5:()=>to,ZP:()=>tr,l9:()=>e1});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(65551),u=n(14163),s=n(9510),c=n(43),d=n(31355),f=n(1359),p=n(32547),h=n(96963),m=n(55509),v=n(25028),y=n(46059),g=n(72942),w=n(8730),b=n(13495),x=n(63376),E=n(42247),C=n(60687),M=["Enter"," "],S=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",...S],A={ltr:[...M,"ArrowRight"],rtl:[...M,"ArrowLeft"]},T={ltr:["ArrowLeft"],rtl:["ArrowRight"]},k="Menu",[P,D,L]=(0,s.N)(k),[j,N]=(0,a.A)(k,[L,m.Bk,g.RG]),O=(0,m.Bk)(),F=(0,g.RG)(),[I,_]=j(k),[W,H]=j(k),z=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=O(t),[s,d]=r.useState(null),f=r.useRef(!1),p=(0,b.c)(a),h=(0,c.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,C.jsx)(m.bL,{...u,children:(0,C.jsx)(I,{scope:t,open:n,onOpenChange:p,content:s,onContentChange:d,children:(0,C.jsx)(W,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:o})})})};z.displayName=k;var B=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=O(n);return(0,C.jsx)(m.Mz,{...o,...r,ref:t})});B.displayName="MenuAnchor";var X="MenuPortal",[K,q]=j(X,{forceMount:void 0}),U=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=_(X,t);return(0,C.jsx)(K,{scope:t,forceMount:n,children:(0,C.jsx)(y.C,{present:n||i.open,children:(0,C.jsx)(v.Z,{asChild:!0,container:o,children:r})})})};U.displayName=X;var G="MenuContent",[V,Y]=j(G),$=r.forwardRef((e,t)=>{let n=q(G,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=_(G,e.__scopeMenu),a=H(G,e.__scopeMenu);return(0,C.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(y.C,{present:r||i.open,children:(0,C.jsx)(P.Slot,{scope:e.__scopeMenu,children:a.modal?(0,C.jsx)(Z,{...o,ref:t}):(0,C.jsx)(J,{...o,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=_(G,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,C.jsx)(ee,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=_(G,e.__scopeMenu);return(0,C.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=(0,w.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:h,onEscapeKeyDown:v,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:b,onDismiss:x,disableOutsideScroll:M,...A}=e,T=_(G,n),k=H(G,n),P=O(n),L=F(n),j=D(n),[N,I]=r.useState(null),W=r.useRef(null),z=(0,i.s)(t,W,T.onContentChange),B=r.useRef(0),X=r.useRef(""),K=r.useRef(0),q=r.useRef(null),U=r.useRef("right"),Y=r.useRef(0),$=M?E.A:r.Fragment,Z=e=>{let t=X.current+e,n=j().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){X.current=t,window.clearTimeout(B.current),""!==t&&(B.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,f.Oh)();let J=r.useCallback(e=>U.current===q.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,s=a.y,c=l.x,d=l.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,q.current?.area),[]);return(0,C.jsx)(V,{scope:n,searchRef:X,onItemEnter:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:r.useCallback(e=>{J(e)||(W.current?.focus(),I(null))},[J]),onTriggerLeave:r.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:K,onPointerGraceIntentChange:r.useCallback(e=>{q.current=e},[]),children:(0,C.jsx)($,{...M?{as:Q,allowPinchZoom:!0}:void 0,children:(0,C.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),W.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:v,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:b,onDismiss:x,children:(0,C.jsx)(g.bL,{asChild:!0,...L,dir:k.dir,orientation:"vertical",loop:a,currentTabStopId:N,onCurrentTabStopIdChange:I,onEntryFocus:(0,o.m)(h,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eT(T.open),"data-radix-menu-content":"",dir:k.dir,...P,...A,ref:z,style:{outline:"none",...A.style},onKeyDown:(0,o.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Z(e.key));let o=W.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let i=j().filter(e=>!e.disabled).map(e=>e.ref.current);S.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),X.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eD(e=>{let t=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(t)&&n&&(U.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});$.displayName=G;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ei=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,s=r.useRef(null),c=H(er,e.__scopeMenu),d=Y(er,e.__scopeMenu),f=(0,i.s)(t,s),p=r.useRef(!1);return(0,C.jsx)(ea,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>a?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||M.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=er;var ea=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...s}=e,c=Y(er,n),d=F(n),f=r.useRef(null),p=(0,i.s)(t,f),[h,m]=r.useState(!1),[v,y]=r.useState("");return r.useEffect(()=>{let e=f.current;e&&y((e.textContent??"").trim())},[s.children]),(0,C.jsx)(P.ItemSlot,{scope:n,disabled:a,textValue:l??v,children:(0,C.jsx)(g.q7,{asChild:!0,...d,focusable:!a,children:(0,C.jsx)(u.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eD(e=>{a?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eD(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),el=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,C.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,C.jsx)(ei,{role:"menuitemcheckbox","aria-checked":ek(n)?"mixed":n,...i,ref:t,"data-state":eP(n),onSelect:(0,o.m)(i.onSelect,()=>r?.(!!ek(n)||!n),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[es,ec]=j(eu,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,b.c)(r);return(0,C.jsx)(es,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,C.jsx)(et,{...o,ref:t})})});ed.displayName=eu;var ef="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=ec(ef,e.__scopeMenu),a=n===i.value;return(0,C.jsx)(em,{scope:e.__scopeMenu,checked:a,children:(0,C.jsx)(ei,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eP(a),onSelect:(0,o.m)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var eh="MenuItemIndicator",[em,ev]=j(eh,{checked:!1}),ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=ev(eh,n);return(0,C.jsx)(y.C,{present:r||ek(i.checked)||!0===i.checked,children:(0,C.jsx)(u.sG.span,{...o,ref:t,"data-state":eP(i.checked)})})});ey.displayName=eh;var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eg.displayName="MenuSeparator";var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=O(n);return(0,C.jsx)(m.i3,{...o,...r,ref:t})});ew.displayName="MenuArrow";var eb="MenuSub",[ex,eE]=j(eb),eC=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:i}=e,a=_(eb,t),l=O(t),[u,s]=r.useState(null),[c,d]=r.useState(null),f=(0,b.c)(i);return r.useEffect(()=>(!1===a.open&&f(!1),()=>f(!1)),[a.open,f]),(0,C.jsx)(m.bL,{...l,children:(0,C.jsx)(I,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,C.jsx)(ex,{scope:t,contentId:(0,h.B)(),triggerId:(0,h.B)(),trigger:u,onTriggerChange:s,children:n})})})};eC.displayName=eb;var eM="MenuSubTrigger",eS=r.forwardRef((e,t)=>{let n=_(eM,e.__scopeMenu),a=H(eM,e.__scopeMenu),l=eE(eM,e.__scopeMenu),u=Y(eM,e.__scopeMenu),s=r.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,C.jsx)(B,{asChild:!0,...f,children:(0,C.jsx)(ea,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eT(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eD(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eD(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;e.disabled||r&&" "===t.key||A[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eS.displayName=eM;var eR="MenuSubContent",eA=r.forwardRef((e,t)=>{let n=q(G,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=_(G,e.__scopeMenu),s=H(G,e.__scopeMenu),c=eE(eR,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,C.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(y.C,{present:a||u.open,children:(0,C.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(ee,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=T[s.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eT(e){return e?"open":"closed"}function ek(e){return"indeterminate"===e}function eP(e){return ek(e)?"indeterminate":e?"checked":"unchecked"}function eD(e){return t=>"mouse"===t.pointerType?e(t):void 0}eA.displayName=eR;var eL="DropdownMenu",[ej,eN]=(0,a.A)(eL,[N]),eO=N(),[eF,eI]=ej(eL),e_=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:s=!0}=e,c=eO(t),d=r.useRef(null),[f,p]=(0,l.i)({prop:i,defaultProp:a??!1,onChange:u,caller:eL});return(0,C.jsx)(eF,{scope:t,triggerId:(0,h.B)(),triggerRef:d,contentId:(0,h.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,C.jsx)(z,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:n})})};e_.displayName=eL;var eW="DropdownMenuTrigger",eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eI(eW,n),s=eO(n);return(0,C.jsx)(B,{asChild:!0,...s,children:(0,C.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eH.displayName=eW;var ez=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eO(t);return(0,C.jsx)(U,{...r,...n})};ez.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eI(eB,n),l=eO(n),u=r.useRef(!1);return(0,C.jsx)($,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||a.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eX.displayName=eB;var eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(et,{...o,...r,ref:t})});eK.displayName="DropdownMenuGroup";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(en,{...o,...r,ref:t})});eq.displayName="DropdownMenuLabel";var eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ei,{...o,...r,ref:t})});eU.displayName="DropdownMenuItem";var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(el,{...o,...r,ref:t})});eG.displayName="DropdownMenuCheckboxItem";var eV=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ed,{...o,...r,ref:t})});eV.displayName="DropdownMenuRadioGroup";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ep,{...o,...r,ref:t})});eY.displayName="DropdownMenuRadioItem";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ey,{...o,...r,ref:t})});e$.displayName="DropdownMenuItemIndicator";var eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eg,{...o,...r,ref:t})});eZ.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eS,{...o,...r,ref:t})});eJ.displayName="DropdownMenuSubTrigger";var eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eA,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eQ.displayName="DropdownMenuSubContent";var e0=e_,e1=eH,e2=ez,e6=eX,e3=eK,e4=eq,e9=eU,e8=eG,e5=eV,e7=eY,te=e$,tt=eZ,tn=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=eO(t),[u,s]=(0,l.i)({prop:r,defaultProp:i??!1,onChange:o,caller:"DropdownMenuSub"});return(0,C.jsx)(eC,{...a,open:u,onOpenChange:s,children:n})},tr=eJ,to=eQ},27351:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]])},28947:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},31355:(e,t,n)=>{n.d(t,{lg:()=>y,qW:()=>f,bL:()=>v});var r,o=n(43210),i=n(70569),a=n(14163),l=n(98599),u=n(13495),s=n(60687),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:v,onInteractOutside:y,onDismiss:g,...w}=e,b=o.useContext(d),[x,E]=o.useState(null),C=x?.ownerDocument??globalThis?.document,[,M]=o.useState({}),S=(0,l.s)(t,e=>E(e)),R=Array.from(b.layers),[A]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),T=R.indexOf(A),k=x?R.indexOf(x):-1,P=b.layersWithOutsidePointerEventsDisabled.size>0,D=k>=T,L=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));D&&!n&&(p?.(e),y?.(e),e.defaultPrevented||g?.())},C),j=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...b.branches].some(e=>e.contains(t))&&(v?.(e),y?.(e),e.defaultPrevented||g?.())},C);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k===b.layers.size-1&&(f?.(e),!e.defaultPrevented&&g&&(e.preventDefault(),g()))},C),o.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),h(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[x,C,n,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),h())},[x,b]),o.useEffect(()=>{let e=()=>M({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(a.sG.div,{...w,ref:S,style:{pointerEvents:P?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(a.sG.div,{...e,ref:i})});function h(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}p.displayName="DismissableLayerBranch";var v=f,y=p},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),o=n(98599),i=n(14163),a=n(13495),l=n(60687),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:y,...g}=e,[w,b]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(y),C=r.useRef(null),M=(0,o.s)(t,e=>b(e)),S=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(S.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:h(C.current,{select:!0})},t=function(e){if(S.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,S.paused]),r.useEffect(()=>{if(w){m.add(S);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,c);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(s,c);w.addEventListener(s,E),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(s,E),m.remove(S)},0)}}},[w,x,E,S]);let R=r.useCallback(e=>{if(!n&&!d||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,S.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...g,ref:M,onKeyDown:R})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},40228:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},41312:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},42247:(e,t,n)=>{n.d(t,{A:()=>U});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(43210)),u="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=i({async:!0,ssr:!1},e),a}(),m=function(){},v=l.forwardRef(function(e,t){var n,r,o,u,s=l.useRef(null),p=l.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],y=p[1],g=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,M=e.sideCar,S=e.noRelative,R=e.noIsolation,A=e.inert,T=e.allowPinchZoom,k=e.as,P=e.gapMode,D=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[s,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}f.set(u,n)},[n]),u),j=i(i({},D),v);return l.createElement(l.Fragment,null,E&&l.createElement(M,{sideCar:h,removeScrollBar:x,shards:C,noRelative:S,noIsolation:R,inert:A,setCallbacks:y,allowPinchZoom:!!T,lockRef:s,gapMode:P}),g?l.cloneElement(l.Children.only(w),i(i({},j),{ref:L})):l.createElement(void 0===k?"div":k,i({},j,{className:b,ref:L}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:s,zeroRight:u};var y=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};y.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},M=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=b(),R="data-scroll-locked",A=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},k=function(){l.useEffect(function(){return document.body.setAttribute(R,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;k();var i=l.useMemo(function(){return M(o)},[o]);return l.createElement(S,{styles:A(i,!t,o,n?"":"!important")})},D=!1;if("undefined"!=typeof window)try{var L=Object.defineProperty({},"passive",{get:function(){return D=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch(e){D=!1}var j=!!D&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},O=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),F(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},F=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,s=t.contains(u),c=!1,d=l>0,f=0,p=0;do{if(!u)break;var h=I(e,u),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&F(e,u)&&(f+=v,p+=m);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},z=function(e){return e&&"current"in e?e.current:e},B=0,X=[];let K=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(B++)[0],i=l.useState(b)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(z),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=W(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],s="deltaY"in e?e.deltaY:l[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=O(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=O(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:s,!0)},[]),s=l.useCallback(function(e){if(X.length&&X[X.length-1]===i){var n="deltaY"in e?H(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(z).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=l.useCallback(function(t){c(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){c(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return X.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,j),document.addEventListener("touchmove",s,j),document.addEventListener("touchstart",d,j),function(){X=X.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,j),document.removeEventListener("touchmove",s,j),document.removeEventListener("touchstart",d,j)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),y);var q=l.forwardRef(function(e,t){return l.createElement(v,i({},e,{ref:t,sideCar:K}))});q.classNames=v.classNames;let U=q},46059:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(43210),o=n(98599),i=n(66156),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(u.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=l(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=l(u.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!s.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(c.current=l(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),s=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return e?.animationName||"none"}a.displayName="Presence"},47313:(e,t,n)=>{n.d(t,{Kq:()=>$,LM:()=>Z,VY:()=>ee,bL:()=>J,bm:()=>en,hE:()=>Q,rc:()=>et});var r=n(43210),o=n(51215),i=n(70569),a=n(98599),l=n(9510),u=n(11273),s=n(31355),c=n(25028),d=n(46059),f=n(14163),p=n(13495),h=n(65551),m=n(66156),v=n(69024),y=n(60687),g="ToastProvider",[w,b,x]=(0,l.N)("Toast"),[E,C]=(0,u.A)("Toast",[x]),[M,S]=E(g),R=e=>{let{__scopeToast:t,label:n="Notification",duration:o=5e3,swipeDirection:i="right",swipeThreshold:a=50,children:l}=e,[u,s]=r.useState(null),[c,d]=r.useState(0),f=r.useRef(!1),p=r.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${g}\`. Expected non-empty \`string\`.`),(0,y.jsx)(w.Provider,{scope:t,children:(0,y.jsx)(M,{scope:t,label:n,duration:o,swipeDirection:i,swipeThreshold:a,toastCount:c,viewport:u,onViewportChange:s,onToastAdd:r.useCallback(()=>d(e=>e+1),[]),onToastRemove:r.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:l})})};R.displayName=g;var A="ToastViewport",T=["F8"],k="toast.viewportPause",P="toast.viewportResume",D=r.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:o=T,label:i="Notifications ({hotkey})",...l}=e,u=S(A,n),c=b(n),d=r.useRef(null),p=r.useRef(null),h=r.useRef(null),m=r.useRef(null),v=(0,a.s)(t,m,u.onViewportChange),g=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=u.toastCount>0;r.useEffect(()=>{let e=e=>{0!==o.length&&o.every(t=>e[t]||e.code===t)&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),r.useEffect(()=>{let e=d.current,t=m.current;if(x&&e&&t){let n=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(k);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},r=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(P);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||r()},i=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",i),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[x,u.isClosePausedRef]);let E=r.useCallback(({tabbingDirection:e})=>{let t=c().map(t=>{let n=t.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===e?r:r.reverse()});return("forwards"===e?t.reverse():t).flat()},[c]);return r.useEffect(()=>{let e=m.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){let n=document.activeElement,r=t.shiftKey;if(t.target===e&&r)return void p.current?.focus();let o=E({tabbingDirection:r?"backwards":"forwards"}),i=o.findIndex(e=>e===n);Y(o.slice(i+1))?t.preventDefault():r?p.current?.focus():h.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,E]),(0,y.jsxs)(s.lg,{ref:d,role:"region","aria-label":i.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,y.jsx)(j,{ref:p,onFocusFromOutsideViewport:()=>{Y(E({tabbingDirection:"forwards"}))}}),(0,y.jsx)(w.Slot,{scope:n,children:(0,y.jsx)(f.sG.ol,{tabIndex:-1,...l,ref:v})}),x&&(0,y.jsx)(j,{ref:h,onFocusFromOutsideViewport:()=>{Y(E({tabbingDirection:"backwards"}))}})]})});D.displayName=A;var L="ToastFocusProxy",j=r.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=S(L,n);return(0,y.jsx)(v.s6,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;i.viewport?.contains(t)||r()}})});j.displayName=L;var N="Toast",O=r.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:o,onOpenChange:a,...l}=e,[u,s]=(0,h.i)({prop:r,defaultProp:o??!0,onChange:a,caller:N});return(0,y.jsx)(d.C,{present:n||u,children:(0,y.jsx)(_,{open:u,...l,ref:t,onClose:()=>s(!1),onPause:(0,p.c)(e.onPause),onResume:(0,p.c)(e.onResume),onSwipeStart:(0,i.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,i.m)(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${n}px`)}),onSwipeCancel:(0,i.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,i.m)(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${n}px`),s(!1)})})})});O.displayName=N;var[F,I]=E(N,{onClose(){}}),_=r.forwardRef((e,t)=>{let{__scopeToast:n,type:l="foreground",duration:u,open:c,onClose:d,onEscapeKeyDown:h,onPause:m,onResume:v,onSwipeStart:g,onSwipeMove:b,onSwipeCancel:x,onSwipeEnd:E,...C}=e,M=S(N,n),[R,A]=r.useState(null),T=(0,a.s)(t,e=>A(e)),D=r.useRef(null),L=r.useRef(null),j=u||M.duration,O=r.useRef(0),I=r.useRef(j),_=r.useRef(0),{onToastAdd:H,onToastRemove:z}=M,B=(0,p.c)(()=>{R?.contains(document.activeElement)&&M.viewport?.focus(),d()}),X=r.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(_.current),O.current=new Date().getTime(),_.current=window.setTimeout(B,e))},[B]);r.useEffect(()=>{let e=M.viewport;if(e){let t=()=>{X(I.current),v?.()},n=()=>{let e=new Date().getTime()-O.current;I.current=I.current-e,window.clearTimeout(_.current),m?.()};return e.addEventListener(k,n),e.addEventListener(P,t),()=>{e.removeEventListener(k,n),e.removeEventListener(P,t)}}},[M.viewport,j,m,v,X]),r.useEffect(()=>{c&&!M.isClosePausedRef.current&&X(j)},[c,j,M.isClosePausedRef,X]),r.useEffect(()=>(H(),()=>z()),[H,z]);let K=r.useMemo(()=>R?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{var r;if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),(r=t).nodeType===r.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!r)if(o){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}),n}(R):null,[R]);return M.viewport?(0,y.jsxs)(y.Fragment,{children:[K&&(0,y.jsx)(W,{__scopeToast:n,role:"status","aria-live":"foreground"===l?"assertive":"polite","aria-atomic":!0,children:K}),(0,y.jsx)(F,{scope:n,onClose:B,children:o.createPortal((0,y.jsx)(w.ItemSlot,{scope:n,children:(0,y.jsx)(s.bL,{asChild:!0,onEscapeKeyDown:(0,i.m)(h,()=>{M.isFocusedToastEscapeKeyDownRef.current||B(),M.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,y.jsx)(f.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":M.swipeDirection,...C,ref:T,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Escape"===e.key&&(h?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(M.isFocusedToastEscapeKeyDownRef.current=!0,B()))}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{0===e.button&&(D.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{if(!D.current)return;let t=e.clientX-D.current.x,n=e.clientY-D.current.y,r=!!L.current,o=["left","right"].includes(M.swipeDirection),i=["left","up"].includes(M.swipeDirection)?Math.min:Math.max,a=o?i(0,t):0,l=o?0:i(0,n),u="touch"===e.pointerType?10:2,s={x:a,y:l},c={originalEvent:e,delta:s};r?(L.current=s,G("toast.swipeMove",b,c,{discrete:!1})):V(s,M.swipeDirection,u)?(L.current=s,G("toast.swipeStart",g,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(n)>u)&&(D.current=null)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=L.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),L.current=null,D.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};V(t,M.swipeDirection,M.swipeThreshold)?G("toast.swipeEnd",E,r,{discrete:!0}):G("toast.swipeCancel",x,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),M.viewport)})]}):null}),W=e=>{let{__scopeToast:t,children:n,...o}=e,i=S(N,t),[a,l]=r.useState(!1),[u,s]=r.useState(!1);return function(e=()=>{}){let t=(0,p.c)(e);(0,m.N)(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>l(!0)),r.useEffect(()=>{let e=window.setTimeout(()=>s(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,y.jsx)(c.Z,{asChild:!0,children:(0,y.jsx)(v.s6,{...o,children:a&&(0,y.jsxs)(y.Fragment,{children:[i.label," ",n]})})})},H=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,y.jsx)(f.sG.div,{...r,ref:t})});H.displayName="ToastTitle";var z=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,y.jsx)(f.sG.div,{...r,ref:t})});z.displayName="ToastDescription";var B="ToastAction",X=r.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,y.jsx)(U,{altText:n,asChild:!0,children:(0,y.jsx)(q,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${B}\`. Expected non-empty \`string\`.`),null)});X.displayName=B;var K="ToastClose",q=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,o=I(K,n);return(0,y.jsx)(U,{asChild:!0,children:(0,y.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,o.onClose)})})});q.displayName=K;var U=r.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...o}=e;return(0,y.jsx)(f.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function G(e,t,n,{discrete:r}){let o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,f.hO)(o,i):o.dispatchEvent(i)}var V=(e,t,n=0)=>{let r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return"left"===t||"right"===t?i&&r>n:!i&&o>n};function Y(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var $=R,Z=D,J=O,Q=H,ee=z,et=X,en=q},48340:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},49625:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},55509:(e,t,n)=>{n.d(t,{Mz:()=>e$,i3:()=>eJ,UC:()=>eZ,bL:()=>eY,Bk:()=>ej});var r=n(43210);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function g(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=m(y(t)),u=v(l),s=p(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(s){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=g*(n&&c?-1:1);break;case"end":r[l]+=g*(n&&c?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),s=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=E(s,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:y,data:g,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:s,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=y?y:d,p={...p,[i]:{...p[i],...g}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(s=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=E(s,f,u)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function M(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=b(h),v=l[p?"floating"===d?"reference":"floating":d],y=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:s,rootBoundary:c,strategy:u})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},C=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:w,strategy:u}):g);return{top:(y.top-C.top+m.top)/E.y,bottom:(C.bottom-y.bottom+m.bottom)/E.y,left:(y.left-C.left+m.left)/E.x,right:(C.right-y.right+m.right)/E.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function R(e){return o.some(t=>e[t]>=0)}async function A(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),u="y"===y(n),s=["left","top"].includes(a)?-1:1,c=i&&u?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof g&&(v="end"===l?-1*g:g),u?{x:v*c,y:m*s}:{x:m*s,y:v*c}}function T(){return"undefined"!=typeof window}function k(e){return L(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(L(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function L(e){return!!T()&&(e instanceof Node||e instanceof P(e).Node)}function j(e){return!!T()&&(e instanceof Element||e instanceof P(e).Element)}function N(e){return!!T()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function O(e){return!!T()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function F(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=W(),n=j(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function W(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(k(e))}function z(e){return P(e).getComputedStyle(e)}function B(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function X(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||O(e)&&e.host||D(e);return O(t)?t.host:t}function K(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=X(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:N(n)&&F(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=P(o);if(i){let e=q(a);return t.concat(a,a.visualViewport||[],F(o)?o:[],e&&n?K(e):[])}return t.concat(o,K(o,[],n))}function q(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=N(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function G(e){return j(e)?e:e.contextElement}function V(e){let t=G(e);if(!N(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=U(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let Y=s(0);function $(e){let t=P(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=G(e),l=s(1);t&&(r?j(r)&&(l=V(r)):l=V(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(a))&&o)?$(a):s(0),c=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=P(a),t=r&&j(r)?P(r):r,n=e,o=q(n);for(;o&&r&&t!==n;){let e=V(o),t=o.getBoundingClientRect(),r=z(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=a,o=q(n=P(o))}}return x({width:f,height:p,x:c,y:d})}function J(e,t){let n=B(e).scrollLeft;return t?t.left+n:Z(D(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=D(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=W();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=B(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===z(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(D(e));else if(j(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=N(e)?V(e):s(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=$(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===z(e).position}function en(e,t){if(!N(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(I(e))return n;if(!N(e)){let t=X(e);for(;t&&!H(t);){if(j(t)&&!et(t))return t;t=X(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(k(r))&&et(r);)r=en(r,t);return r&&H(r)&&et(r)&&!_(r)?n:r||function(e){let t=X(e);for(;N(t)&&!H(t);){if(_(t))return t;if(I(t))break;t=X(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=N(t),o=D(t),i="fixed"===n,a=Z(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!i)if(("body"!==k(t)||F(o))&&(l=B(t)),r){let e=Z(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o));i&&!r&&o&&(u.x=J(o));let c=!o||r||i?s(0):Q(o,l);return{x:a.left+l.scrollLeft-u.x-c.x,y:a.top+l.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=D(r),l=!!t&&I(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},c=s(1),d=s(0),f=N(r);if((f||!f&&!i)&&(("body"!==k(r)||F(a))&&(u=B(r)),N(r))){let e=Z(r);c=V(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?s(0):Q(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-u.scrollTop*c.y+d.y+p.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?I(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=K(e,[],!1).filter(e=>j(e)&&"body"!==k(e)),o=null,i="fixed"===z(e).position,a=i?X(e):e;for(;j(a)&&!H(a);){let t=z(a),n=_(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||F(a)&&!n&&function e(t,n){let r=X(t);return!(r===n||!j(r)||H(r))&&("fixed"===z(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=X(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],s=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,u,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=U(e);return{width:t,height:n}},getScale:V,isElement:j,isRTL:function(e){return"rtl"===z(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:s,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=b(p),w={x:n,y:r},x=m(y(o)),E=v(x),C=await u.getDimensions(d),M="y"===x,S=M?"clientHeight":"clientWidth",R=l.reference[E]+l.reference[x]-w[x]-l.floating[E],A=w[x]-l.reference[x],T=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),k=T?T[S]:0;k&&await (null==u.isElement?void 0:u.isElement(T))||(k=s.floating[S]||l.floating[E]);let P=k/2-C[E]/2-1,D=i(g[M?"top":"left"],P),L=i(g[M?"bottom":"right"],P),j=k-C[E]-L,N=k/2-C[E]/2+(R/2-A/2),O=a(D,i(N,j)),F=!c.arrow&&null!=h(o)&&N!==O&&l.reference[E]/2-(N<D?D:L)-C[E]/2<0,I=F?N<D?N-D:N-j:0;return{[x]:w[x]+I,data:{[x]:O,centerOffset:N-O-I,...F&&{alignmentOffset:I}},reset:F}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var es=n(51215),ec="undefined"!=typeof document?r.useLayoutEffect:function(){};function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await A(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await M(t,c),v=y(p(o)),g=m(v),w=d[g],b=d[v];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}let x=s.fn({...t,[g]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[g]:l,[v]:u}}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:s=!0}=f(e,t),c={x:n,y:r},d=y(o),h=m(d),v=c[h],g=c[d],w=f(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(s){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:b.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?b.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:v,[d]:g}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:C,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:A=!0,...T}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let k=p(l),P=y(c),D=p(c)===c,L=await (null==d.isRTL?void 0:d.isRTL(b.floating)),j=C||(D||!A?[w(c)]:function(e){let t=w(e);return[g(e),t,g(t)]}(c)),N="none"!==R;!C&&N&&j.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(g)))),i}(c,A,R,L));let O=[c,...j],F=await M(t,T),I=[],_=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&I.push(F[k]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(y(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,s,L);I.push(F[e[0]],F[e[1]])}if(_=[..._,{placement:l,overflows:I}],!I.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=O[e];if(t&&("alignment"!==E||P===y(t)||_.every(e=>e.overflows[0]>0&&y(e.placement)===P)))return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(i=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(S){case"bestFit":{let e=null==(a=_.filter(e=>{if(N){let t=y(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:s,platform:c,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),g=await M(t,v),w=p(u),b=h(u),x="y"===y(u),{width:E,height:C}=s.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let S=C-g.top-g.bottom,R=E-g.left-g.right,A=i(C-g[o],S),T=i(E-g[l],R),k=!t.middlewareData.shift,P=A,D=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=R),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=S),k&&!b){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);x?D=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):P=C-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await m({...t,availableWidth:D,availableHeight:P});let L=await c.getDimensions(d.floating);return E!==L.width||C!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=S(await M(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:R(e)}}}case"escaped":{let e=S(await M(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:R(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...em(e),options:[e,t]});var eC=n(14163),eM=n(60687),eS=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eM.jsx)(eC.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eM.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var eR=n(98599),eA=n(11273),eT=n(13495),ek=n(66156),eP=n(18853),eD="Popper",[eL,ej]=(0,eA.A)(eD),[eN,eO]=eL(eD),eF=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eM.jsx)(eN,{scope:t,anchor:o,onAnchorChange:i,children:n})};eF.displayName=eD;var eI="PopperAnchor",e_=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eO(eI,n),l=r.useRef(null),u=(0,eR.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eM.jsx)(eC.sG.div,{...i,ref:u})});e_.displayName=eI;var eW="PopperContent",[eH,ez]=eL(eW),eB=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:s="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:y="optimized",onPlaced:g,...w}=e,b=eO(eW,n),[x,E]=r.useState(null),C=(0,eR.s)(t,e=>E(e)),[M,S]=r.useState(null),R=(0,eP.X)(M),A=R?.width??0,T=R?.height??0,k="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},P=Array.isArray(p)?p:[p],L=P.length>0,j={padding:k,boundary:P.filter(eU),altBoundary:L},{refs:N,floatingStyles:O,placement:F,isPositioned:I,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[m,v]=r.useState(null),[y,g]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),b=r.useCallback(e=>{e!==M.current&&(M.current=e,g(e))},[]),x=a||m,E=l||y,C=r.useRef(null),M=r.useRef(null),S=r.useRef(d),R=null!=s,A=eh(s),T=eh(i),k=eh(c),P=r.useCallback(()=>{if(!C.current||!M.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),eu(C.current,M.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};D.current&&!ed(S.current,t)&&(S.current=t,es.flushSync(()=>{f(t)}))})},[p,t,n,T,k]);ec(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);ec(()=>(D.current=!0,()=>{D.current=!1}),[]),ec(()=>{if(x&&(C.current=x),E&&(M.current=E),x&&E){if(A.current)return A.current(x,E,P);P()}},[x,E,P,A,R]);let L=r.useMemo(()=>({reference:C,floating:M,setReference:w,setFloating:b}),[w,b]),j=r.useMemo(()=>({reference:x,floating:E}),[x,E]),N=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ep(j.floating,d.x),r=ep(j.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,j.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:L,elements:j,floatingStyles:N}),[d,P,L,j,N])}({strategy:"fixed",placement:o+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=G(e),h=l||s?[...p?K(p):[],...K(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=D(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(c||t(),!m||!v)return;let y=u(h),g=u(o.clientWidth-(p+m)),w={rootMargin:-y+"px "+-g+"px "+-u(o.clientHeight-(h+v))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!b)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ea(f,e.getBoundingClientRect())||s(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!f&&y.observe(p),y.observe(t));let g=f?Z(e):null;return f&&function t(){let r=Z(e);g&&!ea(g,r)&&n(),g=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:b.anchor},middleware:[ev({mainAxis:l+T,alignmentAxis:c}),f&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eg():void 0,...j}),f&&ew({...j}),eb({...j,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),M&&eE({element:M,padding:d}),eG({arrowWidth:A,arrowHeight:T}),v&&ex({strategy:"referenceHidden",...j})]}),[W,H]=eV(F),z=(0,eT.c)(g);(0,ek.N)(()=>{I&&z?.()},[I,z]);let B=_.arrow?.x,X=_.arrow?.y,q=_.arrow?.centerOffset!==0,[U,V]=r.useState();return(0,ek.N)(()=>{x&&V(window.getComputedStyle(x).zIndex)},[x]),(0,eM.jsx)("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:I?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eM.jsx)(eH,{scope:n,placedSide:W,onArrowChange:S,arrowX:B,arrowY:X,shouldHideArrow:q,children:(0,eM.jsx)(eC.sG.div,{"data-side":W,"data-align":H,...w,ref:C,style:{...w.style,animation:I?void 0:"none"}})})})});eB.displayName=eW;var eX="PopperArrow",eK={top:"bottom",right:"left",bottom:"top",left:"right"},eq=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ez(eX,n),i=eK[o.placedSide];return(0,eM.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eM.jsx)(eS,{...r,ref:t,style:{...r.style,display:"block"}})})});function eU(e){return null!==e}eq.displayName=eX;var eG=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,s]=eV(n),c={start:"0%",center:"50%",end:"100%"}[s],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===u?(p=i?c:`${d}px`,h=`${-l}px`):"top"===u?(p=i?c:`${d}px`,h=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,h=i?c:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,h=i?c:`${f}px`),{data:{x:p,y:h}}}});function eV(e){let[t,n="center"]=e.split("-");return[t,n]}var eY=eF,e$=e_,eZ=eB,eJ=eq},58559:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},58887:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},63376:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},s=function(e,t,n,r){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var c=a[n],d=[],f=new Set,p=new Set(s),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};s.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(c.get(e)||0)+1;o.set(e,l),c.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=c.get(e)-1;o.set(e,t),c.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),s(o,i,n,"aria-hidden")):function(){return null}}},63851:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},65551:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else l(t)},[s,e,l,u])]}Symbol("RADIX:SYNC_STATE")},65822:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},66156:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(43210),o=globalThis?.document?r.useLayoutEffect:()=>{}},67969:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},68123:(e,t,n)=>{n.d(t,{LM:()=>V,OK:()=>Y,VM:()=>C,bL:()=>G,lr:()=>N});var r=n(43210),o=n(14163),i=n(46059),a=n(11273),l=n(98599),u=n(13495),s=n(43),c=n(66156),d=n(67969),f=n(70569),p=n(60687),h="ScrollArea",[m,v]=(0,a.A)(h),[y,g]=m(h),w=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:i="hover",dir:a,scrollHideDelay:u=600,...c}=e,[d,f]=r.useState(null),[h,m]=r.useState(null),[v,g]=r.useState(null),[w,b]=r.useState(null),[x,E]=r.useState(null),[C,M]=r.useState(0),[S,R]=r.useState(0),[A,T]=r.useState(!1),[k,P]=r.useState(!1),D=(0,l.s)(t,e=>f(e)),L=(0,s.jH)(a);return(0,p.jsx)(y,{scope:n,type:i,dir:L,scrollHideDelay:u,scrollArea:d,viewport:h,onViewportChange:m,content:v,onContentChange:g,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:A,onScrollbarXEnabledChange:T,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:k,onScrollbarYEnabledChange:P,onCornerWidthChange:M,onCornerHeightChange:R,children:(0,p.jsx)(o.sG.div,{dir:L,...c,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":S+"px",...e.style}})})});w.displayName=h;var b="ScrollAreaViewport",x=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:i,nonce:a,...u}=e,s=g(b,n),c=r.useRef(null),d=(0,l.s)(t,c,s.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,p.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...u,ref:d,style:{overflowX:s.scrollbarXEnabled?"scroll":"hidden",overflowY:s.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:s.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var E="ScrollAreaScrollbar",C=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=g(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:l}=i,u="horizontal"===e.orientation;return r.useEffect(()=>(u?a(!0):l(!0),()=>{u?a(!1):l(!1)}),[u,a,l]),"hover"===i.type?(0,p.jsx)(M,{...o,ref:t,forceMount:n}):"scroll"===i.type?(0,p.jsx)(S,{...o,ref:t,forceMount:n}):"auto"===i.type?(0,p.jsx)(R,{...o,ref:t,forceMount:n}):"always"===i.type?(0,p.jsx)(A,{...o,ref:t}):null});C.displayName=E;var M=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,a=g(E,e.__scopeScrollArea),[l,u]=r.useState(!1);return r.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),u(!0)},r=()=>{t=window.setTimeout(()=>u(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[a.scrollArea,a.scrollHideDelay]),(0,p.jsx)(i.C,{present:n||l,children:(0,p.jsx)(R,{"data-state":l?"visible":"hidden",...o,ref:t})})}),S=r.forwardRef((e,t)=>{var n,o;let{forceMount:a,...l}=e,u=g(E,e.__scopeScrollArea),s="horizontal"===e.orientation,c=q(()=>h("SCROLL_END"),100),[d,h]=(n="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((e,t)=>o[e][t]??e,n));return r.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>h("HIDE"),u.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,u.scrollHideDelay,h]),r.useEffect(()=>{let e=u.viewport,t=s?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(h("SCROLL"),c()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[u.viewport,s,h,c]),(0,p.jsx)(i.C,{present:a||"hidden"!==d,children:(0,p.jsx)(A,{"data-state":"hidden"===d?"hidden":"visible",...l,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),R=r.forwardRef((e,t)=>{let n=g(E,e.__scopeScrollArea),{forceMount:o,...a}=e,[l,u]=r.useState(!1),s="horizontal"===e.orientation,c=q(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;u(s?e:t)}},10);return U(n.viewport,c),U(n.content,c),(0,p.jsx)(i.C,{present:o||l,children:(0,p.jsx)(A,{"data-state":l?"visible":"hidden",...a,ref:t})})}),A=r.forwardRef((e,t)=>{let{orientation:n="vertical",...o}=e,i=g(E,e.__scopeScrollArea),a=r.useRef(null),l=r.useRef(0),[u,s]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=H(u.viewport,u.content),d={...o,sizes:u,onSizesChange:s,hasThumb:!!(c>0&&c<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function f(e,t){return function(e,t,n,r="ltr"){let o=z(n),i=t||o/2,a=n.scrollbar.paddingStart+i,l=n.scrollbar.size-n.scrollbar.paddingEnd-(o-i),u=n.content-n.viewport;return X([a,l],"ltr"===r?[0,u]:[-1*u,0])(e)}(e,l.current,u,t)}return"horizontal"===n?(0,p.jsx)(T,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=B(i.viewport.scrollLeft,u,i.dir);a.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=f(e,i.dir))}}):"vertical"===n?(0,p.jsx)(k,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=B(i.viewport.scrollTop,u);a.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=f(e))}}):null}),T=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,a=g(E,e.__scopeScrollArea),[u,s]=r.useState(),c=r.useRef(null),d=(0,l.s)(t,c,a.onScrollbarXChange);return r.useEffect(()=>{c.current&&s(getComputedStyle(c.current))},[c]),(0,p.jsx)(L,{"data-orientation":"horizontal",...i,ref:d,sizes:n,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":z(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{c.current&&a.viewport&&u&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:W(u.paddingLeft),paddingEnd:W(u.paddingRight)}})}})}),k=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,a=g(E,e.__scopeScrollArea),[u,s]=r.useState(),c=r.useRef(null),d=(0,l.s)(t,c,a.onScrollbarYChange);return r.useEffect(()=>{c.current&&s(getComputedStyle(c.current))},[c]),(0,p.jsx)(L,{"data-orientation":"vertical",...i,ref:d,sizes:n,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":z(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{c.current&&a.viewport&&u&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:W(u.paddingTop),paddingEnd:W(u.paddingBottom)}})}})}),[P,D]=m(E),L=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:i,hasThumb:a,onThumbChange:s,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:h,onDragScroll:m,onWheelScroll:v,onResize:y,...w}=e,b=g(E,n),[x,C]=r.useState(null),M=(0,l.s)(t,e=>C(e)),S=r.useRef(null),R=r.useRef(""),A=b.viewport,T=i.content-i.viewport,k=(0,u.c)(v),D=(0,u.c)(h),L=q(y,10);function j(e){S.current&&m({x:e.clientX-S.current.left,y:e.clientY-S.current.top})}return r.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&k(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[A,x,T,k]),r.useEffect(D,[i,D]),U(x,L),U(b.content,L),(0,p.jsx)(P,{scope:n,scrollbar:x,hasThumb:a,onThumbChange:(0,u.c)(s),onThumbPointerUp:(0,u.c)(c),onThumbPositionChange:D,onThumbPointerDown:(0,u.c)(d),children:(0,p.jsx)(o.sG.div,{...w,ref:M,style:{position:"absolute",...w.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),S.current=x.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),j(e))}),onPointerMove:(0,f.m)(e.onPointerMove,j),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,b.viewport&&(b.viewport.style.scrollBehavior=""),S.current=null})})})}),j="ScrollAreaThumb",N=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=D(j,e.__scopeScrollArea);return(0,p.jsx)(i.C,{present:n||o.hasThumb,children:(0,p.jsx)(O,{ref:t,...r})})}),O=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:i,...a}=e,u=g(j,n),s=D(j,n),{onThumbPositionChange:c}=s,d=(0,l.s)(t,e=>s.onThumbChange(e)),h=r.useRef(void 0),m=q(()=>{h.current&&(h.current(),h.current=void 0)},100);return r.useEffect(()=>{let e=u.viewport;if(e){let t=()=>{m(),h.current||(h.current=K(e,c),c())};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[u.viewport,m,c]),(0,p.jsx)(o.sG.div,{"data-state":s.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;s.onThumbPointerDown({x:n,y:r})}),onPointerUp:(0,f.m)(e.onPointerUp,s.onThumbPointerUp)})});N.displayName=j;var F="ScrollAreaCorner",I=r.forwardRef((e,t)=>{let n=g(F,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,p.jsx)(_,{...e,ref:t}):null});I.displayName=F;var _=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,...i}=e,a=g(F,n),[l,u]=r.useState(0),[s,c]=r.useState(0),d=!!(l&&s);return U(a.scrollbarX,()=>{let e=a.scrollbarX?.offsetHeight||0;a.onCornerHeightChange(e),c(e)}),U(a.scrollbarY,()=>{let e=a.scrollbarY?.offsetWidth||0;a.onCornerWidthChange(e),u(e)}),d?(0,p.jsx)(o.sG.div,{...i,ref:t,style:{width:l,height:s,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function W(e){return e?parseInt(e,10):0}function H(e,t){let n=e/t;return isNaN(n)?0:n}function z(e){let t=H(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function B(e,t,n="ltr"){let r=z(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,a=t.content-t.viewport,l=(0,d.q)(e,"ltr"===n?[0,a]:[-1*a,0]);return X([0,a],[0,i-r])(l)}function X(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var K=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},a=n.left!==i.left,l=n.top!==i.top;(a||l)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function q(e,t){let n=(0,u.c)(e),o=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),r.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(n,t)},[n,t])}function U(e,t){let n=(0,u.c)(t);(0,c.N)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}var G=w,V=x,Y=I},69024:(e,t,n)=>{n.d(t,{Qg:()=>a,s6:()=>l});var r=n(43210),o=n(14163),i=n(60687),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...a,...e.style}}));l.displayName="VisuallyHidden"},70569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},72942:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>P,q7:()=>D});var r=n(43210),o=n(70569),i=n(9510),a=n(98599),l=n(11273),u=n(96963),s=n(14163),c=n(13495),d=n(65551),f=n(43),p=n(60687),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[y,g,w]=(0,i.N)(v),[b,x]=(0,l.A)(v,[w]),[E,C]=b(v),M=r.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(S,{...e,ref:t})})}));M.displayName=v;var S=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:u,currentTabStopId:y,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:C=!1,...M}=e,S=r.useRef(null),R=(0,a.s)(t,S),A=(0,f.jH)(u),[T,P]=(0,d.i)({prop:y,defaultProp:w??null,onChange:b,caller:v}),[D,L]=r.useState(!1),j=(0,c.c)(x),N=g(n),O=r.useRef(!1),[F,I]=r.useState(0);return r.useEffect(()=>{let e=S.current;if(e)return e.addEventListener(h,j),()=>e.removeEventListener(h,j)},[j]),(0,p.jsx)(E,{scope:n,orientation:i,dir:A,loop:l,currentTabStopId:T,onItemFocus:r.useCallback(e=>P(e),[P]),onItemShiftTab:r.useCallback(()=>L(!0),[]),onFocusableItemAdd:r.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>I(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:D||0===F?-1:0,"data-orientation":i,...M,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);k([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),C)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>L(!1))})})}),R="RovingFocusGroupItem",A=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:c,...d}=e,f=(0,u.B)(),h=l||f,m=C(R,n),v=m.currentTabStopId===h,w=g(n),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:E}=m;return r.useEffect(()=>{if(i)return b(),()=>x()},[i,b,x]),(0,p.jsx)(y.ItemSlot,{scope:n,id:h,focusable:i,active:a,children:(0,p.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>k(n))}}),children:"function"==typeof c?c({isCurrentTabStop:v,hasTabStop:null!=E}):c})})});A.displayName=R;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function k(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var P=M,D=A},74808:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},78272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},82080:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},84027:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},85778:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86561:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},90103:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]])},93508:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},96882:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},96963:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},97051:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},99270:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},99891:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(18962).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])}};