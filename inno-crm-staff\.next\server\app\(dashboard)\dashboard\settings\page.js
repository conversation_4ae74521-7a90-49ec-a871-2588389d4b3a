(()=>{var e={};e.id=2791,e.ids=[2791],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7146:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var r=a(60687),i=a(43210),t=a(55192),n=a(2553),l=a(68988),d=a(39390),c=a(63974),o=a(84027),x=a(58869),m=a(97051),h=a(99891),u=a(18962);let p=(0,u.A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),j=(0,u.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);function f(){let[e,s]=(0,i.useState)("general"),a=[{id:"general",label:"General",icon:o.A},{id:"profile",label:"Profile",icon:x.A},{id:"notifications",label:"Notifications",icon:m.A},{id:"security",label:"Security",icon:h.A},{id:"system",label:"System",icon:p},{id:"localization",label:"Localization",icon:j}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Settings"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your account and system preferences"})]}),(0,r.jsxs)("div",{className:"flex space-x-6",children:[(0,r.jsx)("div",{className:"w-64 space-y-2",children:a.map(a=>(0,r.jsxs)("button",{onClick:()=>s(a.id),className:`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors ${e===a.id?"bg-blue-50 text-blue-700 border border-blue-200":"text-gray-600 hover:bg-gray-50"}`,children:[(0,r.jsx)(a.icon,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:a.label})]},a.id))}),(0,r.jsxs)("div",{className:"flex-1",children:["general"===e&&(0,r.jsxs)(t.Zp,{children:[(0,r.jsxs)(t.aR,{children:[(0,r.jsx)(t.ZB,{children:"General Settings"}),(0,r.jsx)(t.BT,{children:"Basic system configuration and preferences"})]}),(0,r.jsxs)(t.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"institution-name",children:"Institution Name"}),(0,r.jsx)(l.p,{id:"institution-name",defaultValue:"Innovative Centre"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"academic-year",children:"Academic Year"}),(0,r.jsxs)(c.l6,{defaultValue:"2024",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"2024",children:"2024"}),(0,r.jsx)(c.eb,{value:"2025",children:"2025"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"default-currency",children:"Default Currency"}),(0,r.jsxs)(c.l6,{defaultValue:"UZS",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"UZS",children:"UZS - Uzbek Som"}),(0,r.jsx)(c.eb,{value:"USD",children:"USD - US Dollar"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"timezone",children:"Timezone"}),(0,r.jsxs)(c.l6,{defaultValue:"Asia/Tashkent",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"Asia/Tashkent",children:"Asia/Tashkent"}),(0,r.jsx)(c.eb,{value:"UTC",children:"UTC"})]})]})]})]}),(0,r.jsx)(n.$,{children:"Save Changes"})]})]}),"profile"===e&&(0,r.jsxs)(t.Zp,{children:[(0,r.jsxs)(t.aR,{children:[(0,r.jsx)(t.ZB,{children:"Profile Settings"}),(0,r.jsx)(t.BT,{children:"Manage your personal information"})]}),(0,r.jsxs)(t.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-gray-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(n.$,{variant:"outline",children:"Change Photo"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"JPG, PNG up to 2MB"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"first-name",children:"First Name"}),(0,r.jsx)(l.p,{id:"first-name",defaultValue:"Admin"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"last-name",children:"Last Name"}),(0,r.jsx)(l.p,{id:"last-name",defaultValue:"User"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(l.p,{id:"email",type:"email",defaultValue:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"phone",children:"Phone"}),(0,r.jsx)(l.p,{id:"phone",defaultValue:"+998901234567"})]})]}),(0,r.jsx)(n.$,{children:"Update Profile"})]})]}),"notifications"===e&&(0,r.jsxs)(t.Zp,{children:[(0,r.jsxs)(t.aR,{children:[(0,r.jsx)(t.ZB,{children:"Notification Settings"}),(0,r.jsx)(t.BT,{children:"Configure how you receive notifications"})]}),(0,r.jsxs)(t.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"New Lead Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Get notified when new leads are captured"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Payment Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receive alerts for payment activities"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Class Reminders"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Get reminded about upcoming classes"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"System Updates"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Notifications about system maintenance"})]}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]})]}),(0,r.jsx)(n.$,{children:"Save Preferences"})]})]}),"security"===e&&(0,r.jsxs)(t.Zp,{children:[(0,r.jsxs)(t.aR,{children:[(0,r.jsx)(t.ZB,{children:"Security Settings"}),(0,r.jsx)(t.BT,{children:"Manage your account security"})]}),(0,r.jsxs)(t.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"current-password",children:"Current Password"}),(0,r.jsx)(l.p,{id:"current-password",type:"password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"new-password",children:"New Password"}),(0,r.jsx)(l.p,{id:"new-password",type:"password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"confirm-password",children:"Confirm New Password"}),(0,r.jsx)(l.p,{id:"confirm-password",type:"password"})]})]}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-4",children:"Two-Factor Authentication"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm",children:"Enable 2FA for additional security"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Requires SMS verification"})]}),(0,r.jsx)(n.$,{variant:"outline",children:"Enable 2FA"})]})]}),(0,r.jsx)(n.$,{children:"Update Password"})]})]}),"system"===e&&(0,r.jsxs)(t.Zp,{children:[(0,r.jsxs)(t.aR,{children:[(0,r.jsx)(t.ZB,{children:"System Settings"}),(0,r.jsx)(t.BT,{children:"Configure system-wide settings"})]}),(0,r.jsxs)(t.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"backup-frequency",children:"Backup Frequency"}),(0,r.jsxs)(c.l6,{defaultValue:"daily",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"daily",children:"Daily"}),(0,r.jsx)(c.eb,{value:"weekly",children:"Weekly"}),(0,r.jsx)(c.eb,{value:"monthly",children:"Monthly"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"session-timeout",children:"Session Timeout (minutes)"}),(0,r.jsx)(l.p,{id:"session-timeout",type:"number",defaultValue:"60"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Maintenance Mode"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Temporarily disable system access"})]}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Debug Mode"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Enable detailed error logging"})]}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]})]}),(0,r.jsx)(n.$,{children:"Save System Settings"})]})]}),"localization"===e&&(0,r.jsxs)(t.Zp,{children:[(0,r.jsxs)(t.aR,{children:[(0,r.jsx)(t.ZB,{children:"Localization Settings"}),(0,r.jsx)(t.BT,{children:"Configure language and regional settings"})]}),(0,r.jsxs)(t.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"language",children:"Default Language"}),(0,r.jsxs)(c.l6,{defaultValue:"en",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"en",children:"English"}),(0,r.jsx)(c.eb,{value:"uz",children:"Uzbek"}),(0,r.jsx)(c.eb,{value:"ru",children:"Russian"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"date-format",children:"Date Format"}),(0,r.jsxs)(c.l6,{defaultValue:"dd/mm/yyyy",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"dd/mm/yyyy",children:"DD/MM/YYYY"}),(0,r.jsx)(c.eb,{value:"mm/dd/yyyy",children:"MM/DD/YYYY"}),(0,r.jsx)(c.eb,{value:"yyyy-mm-dd",children:"YYYY-MM-DD"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"number-format",children:"Number Format"}),(0,r.jsxs)(c.l6,{defaultValue:"1,234.56",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"1,234.56",children:"1,234.56"}),(0,r.jsx)(c.eb,{value:"1.234,56",children:"1.234,56"}),(0,r.jsx)(c.eb,{value:"1 234.56",children:"1 234.56"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"first-day",children:"First Day of Week"}),(0,r.jsxs)(c.l6,{defaultValue:"monday",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"sunday",children:"Sunday"}),(0,r.jsx)(c.eb,{value:"monday",children:"Monday"})]})]})]})]}),(0,r.jsx)(n.$,{children:"Save Localization Settings"})]})]})]})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18795:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=a(65239),i=a(48088),t=a(88170),n=a.n(t),l=a(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(s,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,87968)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\settings\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\settings\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28486:(e,s,a)=>{Promise.resolve().then(a.bind(a,7146))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39390:(e,s,a)=>{"use strict";a.d(s,{J:()=>c});var r=a(60687),i=a(43210),t=a(78148),n=a(24224),l=a(96241);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...s},a)=>(0,r.jsx)(t.b,{ref:a,className:(0,l.cn)(d(),e),...s}));c.displayName=t.b.displayName},55192:(e,s,a)=>{"use strict";a.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>l});var r=a(60687),i=a(43210),t=a(96241);let n=i.forwardRef(({className:e,...s},a)=>(0,r.jsx)("div",{ref:a,className:(0,t.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));n.displayName="Card";let l=i.forwardRef(({className:e,...s},a)=>(0,r.jsx)("div",{ref:a,className:(0,t.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=i.forwardRef(({className:e,...s},a)=>(0,r.jsx)("h3",{ref:a,className:(0,t.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=i.forwardRef(({className:e,...s},a)=>(0,r.jsx)("p",{ref:a,className:(0,t.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=i.forwardRef(({className:e,...s},a)=>(0,r.jsx)("div",{ref:a,className:(0,t.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",i.forwardRef(({className:e,...s},a)=>(0,r.jsx)("div",{ref:a,className:(0,t.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},58869:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,s,a)=>{"use strict";a.d(s,{bq:()=>m,eb:()=>j,gC:()=>p,l6:()=>o,yv:()=>x});var r=a(60687),i=a(43210),t=a(22670),n=a(78272),l=a(3589),d=a(13964),c=a(96241);let o=t.bL;t.YJ;let x=t.WT,m=i.forwardRef(({className:e,children:s,...a},i)=>(0,r.jsxs)(t.l9,{ref:i,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,(0,r.jsx)(t.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=t.l9.displayName;let h=i.forwardRef(({className:e,...s},a)=>(0,r.jsx)(t.PP,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));h.displayName=t.PP.displayName;let u=i.forwardRef(({className:e,...s},a)=>(0,r.jsx)(t.wn,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})}));u.displayName=t.wn.displayName;let p=i.forwardRef(({className:e,children:s,position:a="popper",...i},n)=>(0,r.jsx)(t.ZL,{children:(0,r.jsxs)(t.UC,{ref:n,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...i,children:[(0,r.jsx)(h,{}),(0,r.jsx)(t.LM,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(u,{})]})}));p.displayName=t.UC.displayName,i.forwardRef(({className:e,...s},a)=>(0,r.jsx)(t.JU,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=t.JU.displayName;let j=i.forwardRef(({className:e,children:s,...a},i)=>(0,r.jsxs)(t.q7,{ref:i,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(t.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),(0,r.jsx)(t.p4,{children:s})]}));j.displayName=t.q7.displayName,i.forwardRef(({className:e,...s},a)=>(0,r.jsx)(t.wv,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=t.wv.displayName},68988:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var r=a(60687),i=a(43210),t=a(96241);let n=i.forwardRef(({className:e,type:s,...a},i)=>(0,r.jsx)("input",{type:s,className:(0,t.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...a}));n.displayName="Input"},78148:(e,s,a)=>{"use strict";a.d(s,{b:()=>l});var r=a(43210),i=a(14163),t=a(60687),n=r.forwardRef((e,s)=>(0,t.jsx)(i.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},87968:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\settings\\page.tsx","default")},88318:(e,s,a)=>{Promise.resolve().then(a.bind(a,87968))}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[4243,7615,2918,8887,8706,6631],()=>a(18795));module.exports=r})();