"use strict";(()=>{var e={};e.id=6150,e.ids=[6150],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},86310:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>g,serverHooks:()=>f,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{GET:()=>x,POST:()=>h});var n=r(96559),a=r(48088),o=r(37719),u=r(32190),i=r(19854),p=r(41098),d=r(79464),l=r(99326),c=r(45697);let m=c.Ik({userId:c.Yj(),level:c.k5(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]),branch:c.Yj(),emergencyContact:c.Yj().optional(),dateOfBirth:c.Yj().optional(),address:c.Yj().optional(),status:c.k5(["ACTIVE","DROPPED","PAUSED","COMPLETED"]).default("ACTIVE"),currentGroupId:c.Yj().optional()});async function x(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"10"),n=t.get("search"),a=t.get("level"),o=t.get("branch"),i=t.get("status"),p=t.get("paymentStatus"),l="true"===t.get("includeDropped"),c={};l?c.status="DROPPED":i||(c.status={not:"DROPPED"}),n&&(c.OR=[{user:{name:{contains:n,mode:"insensitive"}}},{user:{phone:{contains:n}}},{user:{email:{contains:n,mode:"insensitive"}}}]),a&&(c.level=a),o&&(c.branch="main"===o?"Main Branch":"Branch"),i&&(c.status=i),"UNPAID"===p&&(c.payments={some:{status:"DEBT"}});let[m,x]=await Promise.all([d.z.student.findMany({where:c,include:{user:{select:{id:!0,name:!0,phone:!0,email:!0,createdAt:!0}},enrollments:{include:{group:{include:{course:{select:{name:!0,level:!0}}}}},orderBy:{createdAt:"desc"},take:5},payments:{select:{status:!0,amount:!0,dueDate:!0},orderBy:{createdAt:"desc"},take:10}},orderBy:{createdAt:"desc"},skip:(r-1)*s,take:s}),d.z.student.count({where:c})]),h=await d.z.student.groupBy({by:["status"],_count:{status:!0}}),g=m.map(e=>{let t=e.payments.filter(e=>"DEBT"===e.status),r=t.length>0;return{...e,paymentStatus:r?"UNPAID":"PAID",unpaidAmount:t.reduce((e,t)=>e+Number(t.amount),0)}});return u.NextResponse.json({students:g,pagination:{page:r,limit:s,total:x,pages:Math.ceil(x/s)},statusCounts:h.reduce((e,t)=>(e[t.status]=t._count.status,e),{})})}catch(e){return console.error("Error fetching students:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let t=await (0,i.getServerSession)(p.N);if(!t?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=m.parse(r),n=await d.z.student.create({data:{...s,dateOfBirth:s.dateOfBirth?new Date(s.dateOfBirth):null},include:{user:{select:{name:!0,phone:!0,email:!0}}}});return await l._.logStudentCreated(t.user.id,t.user.role,n.id,{studentName:n.user.name,level:n.level,branch:n.branch},e),u.NextResponse.json(n,{status:201})}catch(e){if(e instanceof c.G)return u.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating student:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/students/route",pathname:"/api/students",filename:"route",bundlePath:"app/api/students/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:y,serverHooks:f}=g;function j(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:y})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,5697,3412,1971],()=>r(86310));module.exports=s})();