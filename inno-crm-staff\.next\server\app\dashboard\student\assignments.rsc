1:"$Sreact.fragment"
2:I[2044,["2108","static/chunks/2108-01f45b4bd0af2163.js","1603","static/chunks/1603-e114a4e36a8b775d.js","7177","static/chunks/app/layout-617fbcf76a0e5f3c.js"],"QueryProvider"]
3:I[8230,["2108","static/chunks/2108-01f45b4bd0af2163.js","1603","static/chunks/1603-e114a4e36a8b775d.js","7177","static/chunks/app/layout-617fbcf76a0e5f3c.js"],"AuthProvider"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[7705,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","6874","static/chunks/6874-1571340a9900ccc2.js","2108","static/chunks/2108-01f45b4bd0af2163.js","9526","static/chunks/9526-48bf5e0dc984c543.js","2198","static/chunks/2198-a256aef0768b8d69.js","9305","static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js"],"BranchProvider"]
7:I[5248,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","6874","static/chunks/6874-1571340a9900ccc2.js","2108","static/chunks/2108-01f45b4bd0af2163.js","9526","static/chunks/9526-48bf5e0dc984c543.js","2198","static/chunks/2198-a256aef0768b8d69.js","9305","static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js"],"Sidebar"]
8:I[2871,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","6874","static/chunks/6874-1571340a9900ccc2.js","2108","static/chunks/2108-01f45b4bd0af2163.js","9526","static/chunks/9526-48bf5e0dc984c543.js","2198","static/chunks/2198-a256aef0768b8d69.js","9305","static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js"],"Header"]
9:I[9800,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","6874","static/chunks/6874-1571340a9900ccc2.js","2108","static/chunks/2108-01f45b4bd0af2163.js","9526","static/chunks/9526-48bf5e0dc984c543.js","2198","static/chunks/2198-a256aef0768b8d69.js","9305","static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js"],"Toaster"]
a:I[9665,[],"MetadataBoundary"]
c:I[9665,[],"OutletBoundary"]
f:I[4911,[],"AsyncMetadataOutlet"]
11:I[9665,[],"ViewportBoundary"]
13:I[6614,[],""]
:HL["/_next/static/css/fd894b6b769b40d1.css","style"]
0:{"P":null,"b":"LXffJSfHYck5VD0io_kNs","p":"","c":["","dashboard","student","assignments"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["dashboard",{"children":["student",{"children":["assignments",{"children":["__PAGE__",{}]}]}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/fd894b6b769b40d1.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","$L6",null,{"children":["$","div",null,{"className":"flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50","children":[["$","$L7",null,{}],["$","div",null,{"className":"flex-1 flex flex-col overflow-hidden","children":[["$","$L8",null,{}],["$","main",null,{"className":"flex-1 overflow-x-hidden overflow-y-auto bg-transparent p-6 lg:p-8","children":["$","div",null,{"className":"max-w-7xl mx-auto","children":["$","div",null,{"className":"fade-in","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:style","children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style","children":404}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style","children":["$","h2",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style","children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],["$","$L9",null,{}]]}]}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["student",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["assignments",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"My Assignments"}],["$","p",null,{"className":"text-gray-600","children":"Track and submit your course assignments"}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-4 gap-6","children":[["$","div",null,{"ref":"$undefined","className":"rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm","children":[["$","div",null,{"ref":"$undefined","className":"p-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","h3",null,{"ref":"$undefined","className":"tracking-tight text-sm font-medium","children":"Pending"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 text-orange-600","children":[["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],["$","polyline","68esgv",{"points":"12 6 12 12 16 14"}],"$undefined"]}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":[["$","div",null,{"className":"text-2xl font-bold","children":2}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"Due soon"}]]}]]}],["$","div",null,{"ref":"$undefined","className":"rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm","children":[["$","div",null,{"ref":"$undefined","className":"p-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","h3",null,{"ref":"$undefined","className":"tracking-tight text-sm font-medium","children":"Submitted"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-upload h-4 w-4 text-blue-600","children":[["$","path","ih7n3h",{"d":"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["$","polyline","t8dd8p",{"points":"17 8 12 3 7 8"}],["$","line","widbto",{"x1":"12","x2":"12","y1":"3","y2":"15"}],"$undefined"]}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":[["$","div",null,{"className":"text-2xl font-bold","children":2}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"Awaiting grades"}]]}]]}],["$","div",null,{"ref":"$undefined","className":"rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm","children":[["$","div",null,{"ref":"$undefined","className":"p-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","h3",null,{"ref":"$undefined","className":"tracking-tight text-sm font-medium","children":"Graded"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-check-circle h-4 w-4 text-green-600","children":[["$","path","g774vq",{"d":"M22 11.08V12a10 10 0 1 1-5.93-9.14"}],["$","path","1pflzl",{"d":"m9 11 3 3L22 4"}],"$undefined"]}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":[["$","div",null,{"className":"text-2xl font-bold","children":2}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"Completed"}]]}]]}],["$","div",null,{"ref":"$undefined","className":"rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm","children":[["$","div",null,{"ref":"$undefined","className":"p-6 flex flex-row items-center justify-between space-y-0 pb-2","children":[["$","h3",null,{"ref":"$undefined","className":"tracking-tight text-sm font-medium","children":"Upcoming"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-4 w-4 text-gray-600","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":[["$","div",null,{"className":"text-2xl font-bold","children":1}],["$","p",null,{"className":"text-xs text-muted-foreground","children":"Future assignments"}]]}]]}]]}],["$","div",null,{"ref":"$undefined","className":"rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-5 w-5 mr-2 text-orange-600","children":[["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],["$","polyline","68esgv",{"points":"12 6 12 12 16 14"}],"$undefined"]}],"Pending Assignments"]}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"Assignments that need to be completed"}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"space-y-4","children":[["$","div","1",{"className":"flex items-center justify-between p-4 border rounded-lg bg-orange-50","children":[["$","div",null,{"className":"flex items-center space-x-4","children":[["$","div",null,{"className":"flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4","children":[["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],["$","polyline","68esgv",{"points":"12 6 12 12 16 14"}],"$undefined"]}]}],["$","div",null,{"className":"flex-1","children":[["$","h4",null,{"className":"font-medium","children":"Essay: My Dream Vacation"}],["$","p",null,{"className":"text-sm text-gray-600","children":"General English B1"}],["$","p",null,{"className":"text-sm text-gray-500 mt-1","children":"Write a 300-word essay about your dream vacation destination"}],["$","div",null,{"className":"flex items-center space-x-4 mt-2","children":[["$","div",null,{"className":"flex items-center space-x-1","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-3 w-3 text-gray-400","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}],["$","span",null,{"className":"text-xs text-gray-500","children":["Due: ","2024-01-20"]}]]}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground px-2.5 py-0.5 text-xs","children":"Essay"}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 px-2.5 py-0.5 text-xs bg-red-100 text-red-800","children":["high"," priority"]}]]}]]}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-sm font-medium","children":[100," pts"]}],["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02] h-8 rounded-lg px-3 text-xs","ref":"$undefined","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-upload h-4 w-4 mr-1","children":[["$","path","ih7n3h",{"d":"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["$","polyline","t8dd8p",{"points":"17 8 12 3 7 8"}],["$","line","widbto",{"x1":"12","x2":"12","y1":"3","y2":"15"}],"$undefined"]}],"Submit"]}]]}]]}],["$","div","2",{"className":"flex items-center justify-between p-4 border rounded-lg bg-orange-50","children":[["$","div",null,{"className":"flex items-center space-x-4","children":[["$","div",null,{"className":"flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4","children":[["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],["$","polyline","68esgv",{"points":"12 6 12 12 16 14"}],"$undefined"]}]}],["$","div",null,{"className":"flex-1","children":[["$","h4",null,{"className":"font-medium","children":"Vocabulary Quiz: Unit 6"}],["$","p",null,{"className":"text-sm text-gray-600","children":"General English B1"}],["$","p",null,{"className":"text-sm text-gray-500 mt-1","children":"Complete the vocabulary quiz covering travel and tourism terms"}],["$","div",null,{"className":"flex items-center space-x-4 mt-2","children":[["$","div",null,{"className":"flex items-center space-x-1","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-3 w-3 text-gray-400","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}],["$","span",null,{"className":"text-xs text-gray-500","children":["Due: ","2024-01-24"]}]]}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground px-2.5 py-0.5 text-xs","children":"Quiz"}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 px-2.5 py-0.5 text-xs bg-yellow-100 text-yellow-800","children":["medium"," priority"]}]]}]]}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-sm font-medium","children":[50," pts"]}],["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02] h-8 rounded-lg px-3 text-xs","ref":"$undefined","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-upload h-4 w-4 mr-1","children":[["$","path","ih7n3h",{"d":"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["$","polyline","t8dd8p",{"points":"17 8 12 3 7 8"}],["$","line","widbto",{"x1":"12","x2":"12","y1":"3","y2":"15"}],"$undefined"]}],"Submit"]}]]}]]}]]}]}]]}],["$","div",null,{"ref":"$undefined","className":"rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-check-circle h-5 w-5 mr-2 text-green-600","children":[["$","path","g774vq",{"d":"M22 11.08V12a10 10 0 1 1-5.93-9.14"}],["$","path","1pflzl",{"d":"m9 11 3 3L22 4"}],"$undefined"]}],"Submitted Assignments"]}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"Your completed assignments and grades"}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"space-y-4","children":[["$","div","3",{"className":"flex items-center justify-between p-4 border rounded-lg","children":[["$","div",null,{"className":"flex items-center space-x-4","children":[["$","div",null,{"className":"flex items-center justify-center w-12 h-12 bg-green-100 rounded-full","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-check-circle h-4 w-4","children":[["$","path","g774vq",{"d":"M22 11.08V12a10 10 0 1 1-5.93-9.14"}],["$","path","1pflzl",{"d":"m9 11 3 3L22 4"}],"$undefined"]}]}],["$","div",null,{"className":"flex-1","children":[["$","h4",null,{"className":"font-medium","children":"Grammar Exercise: Present Perfect"}],["$","p",null,{"className":"text-sm text-gray-600","children":"General English B1"}],["$","div",null,{"className":"flex items-center space-x-4 mt-1","children":[["$","div",null,{"className":"flex items-center space-x-1","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-3 w-3 text-gray-400","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}],["$","span",null,{"className":"text-xs text-gray-500","children":["Submitted: ","2024-01-12"]}]]}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground px-2.5 py-0.5 text-xs","children":"Exercise"}]]}],["$","div",null,{"className":"mt-2 p-2 bg-gray-50 rounded text-sm","children":[["$","strong",null,{"children":"Feedback:"}]," ","Good understanding of present perfect tense. Work on irregular verbs."]}]]}]]}],["$","div",null,{"className":"flex items-center space-x-4","children":[["$","div",null,{"className":"text-right","children":[["$","div",null,{"className":"text-lg font-bold text-green-600","children":[85,"/",100]}],["$","div",null,{"className":"text-sm text-gray-500","children":[85,"%"]}]]}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 px-2.5 py-0.5 text-xs bg-green-100 text-green-800","children":"graded"}],["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs","ref":"$undefined","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-eye h-4 w-4 mr-1","children":[["$","path","rwhkz3",{"d":"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"}],["$","circle","1v7zrd",{"cx":"12","cy":"12","r":"3"}],"$undefined"]}],"View"]}]]}]]}],["$","div","4",{"className":"flex items-center justify-between p-4 border rounded-lg","children":[["$","div",null,{"className":"flex items-center space-x-4","children":[["$","div",null,{"className":"flex items-center justify-center w-12 h-12 bg-green-100 rounded-full","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-check-circle h-4 w-4","children":[["$","path","g774vq",{"d":"M22 11.08V12a10 10 0 1 1-5.93-9.14"}],["$","path","1pflzl",{"d":"m9 11 3 3L22 4"}],"$undefined"]}]}],["$","div",null,{"className":"flex-1","children":[["$","h4",null,{"className":"font-medium","children":"Speaking Recording: Describing Places"}],["$","p",null,{"className":"text-sm text-gray-600","children":"General English B1"}],["$","div",null,{"className":"flex items-center space-x-4 mt-1","children":[["$","div",null,{"className":"flex items-center space-x-1","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-3 w-3 text-gray-400","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}],["$","span",null,{"className":"text-xs text-gray-500","children":["Submitted: ","2024-01-10"]}]]}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground px-2.5 py-0.5 text-xs","children":"Speaking"}]]}],["$","div",null,{"className":"mt-2 p-2 bg-gray-50 rounded text-sm","children":[["$","strong",null,{"children":"Feedback:"}]," ","Clear pronunciation. Try to use more descriptive adjectives."]}]]}]]}],["$","div",null,{"className":"flex items-center space-x-4","children":[["$","div",null,{"className":"text-right","children":[["$","div",null,{"className":"text-lg font-bold text-yellow-600","children":[78,"/",100]}],["$","div",null,{"className":"text-sm text-gray-500","children":[78,"%"]}]]}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 px-2.5 py-0.5 text-xs bg-green-100 text-green-800","children":"graded"}],["$","button",null,{"className":"inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 h-8 rounded-lg px-3 text-xs","ref":"$undefined","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-eye h-4 w-4 mr-1","children":[["$","path","rwhkz3",{"d":"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"}],["$","circle","1v7zrd",{"cx":"12","cy":"12","r":"3"}],"$undefined"]}],"View"]}]]}]]}]]}]}]]}],["$","div",null,{"ref":"$undefined","className":"rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":[["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-5 w-5 mr-2 text-gray-600","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}],"Upcoming Assignments"]}],["$","p",null,{"ref":"$undefined","className":"text-sm text-muted-foreground","children":"Future assignments to be released"}]]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"space-y-4","children":[["$","div","5",{"className":"flex items-center justify-between p-4 border rounded-lg","children":[["$","div",null,{"className":"flex items-center space-x-4","children":[["$","div",null,{"className":"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-4 w-4","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}]}],["$","div",null,{"children":[["$","h4",null,{"className":"font-medium","children":"Reading Comprehension Test"}],["$","p",null,{"className":"text-sm text-gray-600","children":"General English B1"}],["$","p",null,{"className":"text-sm text-gray-500 mt-1","children":"Test on reading comprehension skills"}],["$","div",null,{"className":"flex items-center space-x-4 mt-2","children":[["$","div",null,{"className":"flex items-center space-x-1","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-3 w-3 text-gray-400","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}],["$","span",null,{"className":"text-xs text-gray-500","children":["Available: ","2024-01-25"]}]]}],["$","div",null,{"className":"flex items-center space-x-1","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-3 w-3 text-gray-400","children":[["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],["$","polyline","68esgv",{"points":"12 6 12 12 16 14"}],"$undefined"]}],["$","span",null,{"className":"text-xs text-gray-500","children":["Due: ","2024-01-30"]}]]}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground px-2.5 py-0.5 text-xs","children":"Test"}]]}]]}]]}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","span",null,{"className":"text-sm font-medium","children":[100," pts"]}],["$","div",null,{"className":"inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent hover:bg-primary/80 px-2.5 py-0.5 text-xs bg-gray-100 text-gray-800","children":"upcoming"}]]}]]}]]}]}]]}],["$","div",null,{"ref":"$undefined","className":"rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm","children":[["$","div",null,{"ref":"$undefined","className":"flex flex-col space-y-1.5 p-6","children":["$","h3",null,{"ref":"$undefined","className":"text-2xl font-semibold leading-none tracking-tight","children":"Quick Actions"}]}],["$","div",null,{"ref":"$undefined","className":"p-6 pt-0","children":["$","div",null,{"className":"grid grid-cols-2 md:grid-cols-4 gap-4","children":[["$","button",null,{"className":"items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 px-4 py-2 h-16 flex flex-col space-y-1","ref":"$undefined","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-upload h-5 w-5","children":[["$","path","ih7n3h",{"d":"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["$","polyline","t8dd8p",{"points":"17 8 12 3 7 8"}],["$","line","widbto",{"x1":"12","x2":"12","y1":"3","y2":"15"}],"$undefined"]}],["$","span",null,{"className":"text-sm","children":"Submit Assignment"}]]}],["$","button",null,{"className":"items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 px-4 py-2 h-16 flex flex-col space-y-1","ref":"$undefined","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-download h-5 w-5","children":[["$","path","ih7n3h",{"d":"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["$","polyline","2ggqvy",{"points":"7 10 12 15 17 10"}],["$","line","1vk2je",{"x1":"12","x2":"12","y1":"15","y2":"3"}],"$undefined"]}],["$","span",null,{"className":"text-sm","children":"Download Materials"}]]}],["$","button",null,{"className":"items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 px-4 py-2 h-16 flex flex-col space-y-1","ref":"$undefined","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-book-open h-5 w-5","children":[["$","path","vv98re",{"d":"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}],["$","path","1cyq3y",{"d":"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"}],"$undefined"]}],["$","span",null,{"className":"text-sm","children":"Study Resources"}]]}],["$","button",null,{"className":"items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300 px-4 py-2 h-16 flex flex-col space-y-1","ref":"$undefined","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar h-5 w-5","children":[["$","rect","eu3xkr",{"width":"18","height":"18","x":"3","y":"4","rx":"2","ry":"2"}],["$","line","m3sa8f",{"x1":"16","x2":"16","y1":"2","y2":"6"}],["$","line","18kwsl",{"x1":"8","x2":"8","y1":"2","y2":"6"}],["$","line","xt86sb",{"x1":"3","x2":"21","y1":"10","y2":"10"}],"$undefined"]}],["$","span",null,{"className":"text-sm","children":"Assignment Calendar"}]]}]]}]}]]}]]}],["$","$La",null,{"children":"$Lb"}],null,["$","$Lc",null,{"children":["$Ld","$Le",["$","$Lf",null,{"promise":"$@10"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","G8UjtQMeQa9jvjQ6XHZls",{"children":[["$","$L11",null,{"children":"$L12"}],null]}],null]}],false]],"m":"$undefined","G":["$13","$undefined"],"s":false,"S":true}
14:"$Sreact.suspense"
15:I[4911,[],"AsyncMetadata"]
b:["$","$14",null,{"fallback":null,"children":["$","$L15",null,{"promise":"$@16"}]}]
e:null
12:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
d:null
16:{"metadata":[["$","title","0",{"children":"Innovative Centre CRM"}],["$","meta","1",{"name":"description","content":"Customer Relationship Management System for Innovative Centre"}]],"error":null,"digest":"$undefined"}
10:{"metadata":"$16:metadata","error":null,"digest":"$undefined"}
