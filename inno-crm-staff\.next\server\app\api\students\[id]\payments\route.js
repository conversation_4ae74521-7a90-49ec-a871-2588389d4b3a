(()=>{var e={};e.id=458,e.ids=[458],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30746:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(79464);async function p(e,{params:t}){try{let{id:e}=await t,r=await u.z.student.findUnique({where:{id:e},include:{payments:{orderBy:{createdAt:"desc"},take:20}}});if(!r)return i.NextResponse.json({error:"Student not found"},{status:404});let s=r.payments.filter(e=>"DEBT"===e.status).reduce((e,t)=>e+Number(t.amount),0);return i.NextResponse.json({payments:r.payments,unpaidAmount:s,totalPayments:r.payments.length})}catch(e){return console.error("Error fetching student payments:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/students/[id]/payments/route",pathname:"/api/students/[id]/payments",filename:"route",bundlePath:"app/api/students/[id]/payments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\payments\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:m}=d;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580],()=>r(30746));module.exports=s})();