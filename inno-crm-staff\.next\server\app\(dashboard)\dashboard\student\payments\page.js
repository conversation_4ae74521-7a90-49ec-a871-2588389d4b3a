(()=>{var e={};e.id=6613,e.ids=[6613],e.modules={439:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5187:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>d.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(65239),a=r(48088),n=r(88170),d=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let o={children:["",{children:["(dashboard)",{children:["dashboard",{children:["student",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43135)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\payments\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\payments\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/student/payments/page",pathname:"/dashboard/student/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40918:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},43135:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(37413),a=r(51358),n=r(99455),d=r(94592),i=r(439),l=r(53148),o=r(89217),c=r(18618);let x=(0,c.A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var m=r(40918);let u=(0,c.A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var h=r(65516);function p(){let e=24e5,s=18e5,r=6e5,c="2024-02-01",p=4,f=6e5,g=3,y=1,j=e=>{switch(e){case"paid":return"bg-green-100 text-green-800";case"pending":return"bg-orange-100 text-orange-800";case"overdue":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},b=e=>{switch(e){case"paid":return(0,t.jsx)(i.A,{className:"h-4 w-4"});case"pending":default:return(0,t.jsx)(l.A,{className:"h-4 w-4"});case"overdue":return(0,t.jsx)(o.A,{className:"h-4 w-4"})}},v=e=>e.toLocaleString()+" UZS";return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Payments"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Track your payment history and upcoming dues"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Total Amount"}),(0,t.jsx)(x,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:v(e)}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Course fee"})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Paid Amount"}),(0,t.jsx)(i.A,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:v(s)}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(s/e*100),"% completed"]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Pending Amount"}),(0,t.jsx)(o.A,{className:"h-4 w-4 text-orange-600"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:v(r)}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Remaining balance"})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Next Due Date"}),(0,t.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:c}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Upcoming payment"})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Payment Progress"}),(0,t.jsx)(a.BT,{children:"Your payment plan progress"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Installments Paid"}),(0,t.jsxs)("span",{className:"font-medium",children:[g," of ",p]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,t.jsx)("div",{className:"bg-green-500 h-3 rounded-full",style:{width:`${g/p*100}%`}})}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,t.jsxs)("span",{children:["Installment Amount: ",v(f)]}),(0,t.jsxs)("span",{children:["Remaining: ",y," payments"]})]})]})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center",children:[(0,t.jsx)(u,{className:"h-5 w-5 mr-2"}),"Payment History"]}),(0,t.jsx)(a.BT,{children:"All your payment transactions"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{id:1,date:"2024-01-15",amount:6e5,course:"General English B1",method:"Bank Transfer",status:"paid",receiptNumber:"RCP-2024-001"},{id:2,date:"2024-01-01",amount:6e5,course:"General English B1",method:"Cash",status:"paid",receiptNumber:"RCP-2024-002"},{id:3,date:"2023-12-15",amount:6e5,course:"General English B1",method:"Bank Transfer",status:"paid",receiptNumber:"RCP-2023-045"},{id:4,date:"2024-02-01",amount:6e5,course:"General English B1",method:"Pending",status:"pending",receiptNumber:null}].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[b(e.status),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:e.course}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.date}),e.receiptNumber&&(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Receipt: ",e.receiptNumber]})]})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"font-medium",children:v(e.amount)}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.method})]}),(0,t.jsx)(n.E,{className:j(e.status),children:e.status}),"paid"===e.status&&(0,t.jsxs)(d.$,{size:"sm",variant:"outline",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Receipt"]})]})]},e.id))})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Payment Methods"}),(0,t.jsx)(a.BT,{children:"Available payment options"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"p-4 border rounded-lg text-center",children:[(0,t.jsx)(u,{className:"h-8 w-8 mx-auto mb-2 text-blue-600"}),(0,t.jsx)("h4",{className:"font-medium",children:"Bank Transfer"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Direct bank transfer"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg text-center",children:[(0,t.jsx)(x,{className:"h-8 w-8 mx-auto mb-2 text-green-600"}),(0,t.jsx)("h4",{className:"font-medium",children:"Cash Payment"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Pay at reception"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg text-center",children:[(0,t.jsx)(u,{className:"h-8 w-8 mx-auto mb-2 text-purple-600"}),(0,t.jsx)("h4",{className:"font-medium",children:"Online Payment"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Credit/Debit card"})]})]})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsx)(a.ZB,{children:"Quick Actions"})}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,t.jsxs)(d.$,{children:[(0,t.jsx)(u,{className:"h-4 w-4 mr-2"}),"Make Payment"]}),(0,t.jsxs)(d.$,{variant:"outline",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Download All Receipts"]}),(0,t.jsxs)(d.$,{variant:"outline",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"View Payment Schedule"]})]})})]})]})}},51358:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>d,aR:()=>i});var t=r(37413),a=r(61120),n=r(66819);let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));d.displayName="Card";let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));l.displayName="CardTitle";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65516:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},66819:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(75986),a=r(8974);function n(...e){return(0,a.QP)((0,t.$)(e))}},78335:()=>{},89217:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94592:(e,s,r)=>{"use strict";r.d(s,{$:()=>o});var t=r(37413),a=r(61120),n=r(70403),d=r(50662),i=r(66819);let l=(0,d.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef(({className:e,variant:s,size:r,asChild:a=!1,...d},o)=>{let c=a?n.DX:"button";return(0,t.jsx)(c,{className:(0,i.cn)(l({variant:s,size:r,className:e})),ref:o,...d})});o.displayName="Button"},96487:()=>{},99455:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(37413);r(61120);var a=r(50662),n=r(66819);let d=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i({className:e,variant:s,...r}){return(0,t.jsx)("div",{className:(0,n.cn)(d({variant:s}),e),...r})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,7144,6631],()=>r(5187));module.exports=t})();