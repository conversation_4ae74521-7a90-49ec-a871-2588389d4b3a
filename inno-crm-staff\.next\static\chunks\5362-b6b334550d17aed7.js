"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5362],{88:(e,s,a)=>{a.d(s,{d:()=>n});var l=a(5155),r=a(2115),i=a(4884),t=a(3999);let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)(i.bL,{className:(0,t.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...r,ref:s,children:(0,l.jsx)(i.zi,{className:(0,t.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=i.bL.displayName},1039:(e,s,a)=>{a.d(s,{A:()=>k});var l=a(5155),r=a(2115),i=a(2177),t=a(221),n=a(1153),c=a(7168),d=a(9852),o=a(2714),m=a(5784),u=a(9474),p=a(8482),x=a(9026),h=a(88),b=a(5040),v=a(4186),j=a(5868),f=a(7624);let N=n.Ik({name:n.Yj().min(2,"Course name must be at least 2 characters"),level:n.k5(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]),description:n.Yj().optional(),duration:n.ai().min(1,"Duration must be at least 1 week").max(52,"Duration cannot exceed 52 weeks"),price:n.ai().min(0,"Price cannot be negative"),isActive:n.zM().default(!0)}),g=[{value:"A1",label:"A1 - Beginner",description:"Basic level for complete beginners"},{value:"A2",label:"A2 - Elementary",description:"Elementary level with basic communication skills"},{value:"B1",label:"B1 - Intermediate",description:"Intermediate level for everyday communication"},{value:"B2",label:"B2 - Upper Intermediate",description:"Upper intermediate with complex topics"},{value:"IELTS",label:"IELTS",description:"IELTS preparation course"},{value:"SAT",label:"SAT Preparation",description:"SAT test preparation course"},{value:"MATH",label:"Mathematics",description:"Mathematics course for various levels"},{value:"KIDS",label:"Kids English",description:"English course designed for children"}],w=[{weeks:4,label:"1 Month (4 weeks)"},{weeks:8,label:"2 Months (8 weeks)"},{weeks:12,label:"3 Months (12 weeks)"},{weeks:16,label:"4 Months (16 weeks)"},{weeks:20,label:"5 Months (20 weeks)"},{weeks:24,label:"6 Months (24 weeks)"},{weeks:36,label:"9 Months (36 weeks)"},{weeks:48,label:"1 Year (48 weeks)"}],y=[{price:5e5,label:"$40/month (500,000 UZS)"},{price:625e3,label:"$50/month (625,000 UZS)"},{price:75e4,label:"$60/month (750,000 UZS)"},{price:875e3,label:"$70/month (875,000 UZS)"},{price:1e6,label:"$80/month (1,000,000 UZS)"},{price:125e4,label:"$100/month (1,250,000 UZS)"},{price:15e5,label:"$120/month (1,500,000 UZS)"},{price:2e6,label:"$160/month (2,000,000 UZS)"}];function k(e){var s;let{initialData:a,onSubmit:n,onCancel:k,isEditing:A=!1}=e,[S,C]=(0,r.useState)(!1),[T,E]=(0,r.useState)(null),{register:U,handleSubmit:$,setValue:I,watch:F,formState:{errors:B}}=(0,i.mN)({resolver:(0,t.u)(N),defaultValues:{name:(null==a?void 0:a.name)||"",level:(null==a?void 0:a.level)||"A1",description:(null==a?void 0:a.description)||"",duration:(null==a?void 0:a.duration)||12,price:(null==a?void 0:a.price)||75e4,isActive:null==(s=null==a?void 0:a.isActive)||s}}),M=F("level"),R=F("duration"),Z=F("price"),D=F("isActive"),L=async e=>{C(!0),E(null);try{await n(e)}catch(e){E(e instanceof Error?e.message:"An error occurred")}finally{C(!1)}},J=g.find(e=>e.value===M);return(0,l.jsxs)(p.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,l.jsxs)(p.aR,{children:[(0,l.jsxs)(p.ZB,{className:"flex items-center",children:[(0,l.jsx)(b.A,{className:"h-5 w-5 mr-2"}),A?"Edit Course":"Create New Course"]}),(0,l.jsx)(p.BT,{children:A?"Update course information":"Enter course details to create a new course offering"})]}),(0,l.jsx)(p.Wu,{children:(0,l.jsxs)("form",{onSubmit:$(L),className:"space-y-6",children:[T&&(0,l.jsx)(x.Fc,{variant:"destructive",children:(0,l.jsx)(x.TN,{children:T})}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)(b.A,{className:"h-4 w-4 text-gray-500"}),(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Course Information"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"name",children:"Course Name *"}),(0,l.jsx)(d.p,{id:"name",...U("name"),placeholder:"e.g., General English A1, IELTS Preparation",className:B.name?"border-red-500":""}),B.name&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:B.name.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"level",children:"Level *"}),(0,l.jsxs)(m.l6,{value:M,onValueChange:e=>I("level",e),children:[(0,l.jsx)(m.bq,{className:B.level?"border-red-500":"",children:(0,l.jsx)(m.yv,{placeholder:"Select level"})}),(0,l.jsx)(m.gC,{children:g.map(e=>(0,l.jsx)(m.eb,{value:e.value,children:e.label},e.value))})]}),B.level&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:B.level.message}),J&&(0,l.jsx)("p",{className:"text-sm text-gray-600",children:J.description})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"description",children:"Description"}),(0,l.jsx)(u.T,{id:"description",...U("description"),placeholder:"Describe the course content, objectives, and target audience...",className:"min-h-[100px]"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(h.d,{id:"isActive",checked:D,onCheckedChange:e=>I("isActive",e)}),(0,l.jsx)(o.J,{htmlFor:"isActive",children:"Active Course"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)(v.A,{className:"h-4 w-4 text-gray-500"}),(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Duration & Pricing"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"duration",children:"Duration (weeks) *"}),(0,l.jsx)(d.p,{id:"duration",type:"number",min:"1",max:"52",...U("duration",{valueAsNumber:!0}),className:B.duration?"border-red-500":""}),B.duration&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:B.duration.message}),(0,l.jsx)("div",{className:"grid grid-cols-2 gap-2 mt-2",children:w.slice(0,4).map(e=>(0,l.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>I("duration",e.weeks),className:R===e.weeks?"bg-blue-50 border-blue-300":"",children:[e.weeks,"w"]},e.weeks))})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{htmlFor:"price",children:"Price (UZS) *"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"price",type:"number",min:"0",step:"50000",...U("price",{valueAsNumber:!0}),className:"pl-10 ".concat(B.price?"border-red-500":"")})]}),B.price&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:B.price.message}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["≈ $",(Z/12500).toFixed(0)," USD"]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(o.J,{children:"Quick Price Selection"}),(0,l.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:y.map(e=>(0,l.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>I("price",e.price),className:Z===e.price?"bg-green-50 border-green-300":"",children:["$",(e.price/12500).toFixed(0)]},e.price))})]})]}),(0,l.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Course Summary"}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Level:"}),(0,l.jsx)("span",{className:"ml-2 font-medium",children:null==J?void 0:J.label})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Duration:"}),(0,l.jsxs)("span",{className:"ml-2 font-medium",children:[R," weeks"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Price:"}),(0,l.jsxs)("span",{className:"ml-2 font-medium",children:[Z.toLocaleString()," UZS ($",(Z/12500).toFixed(0),")"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Status:"}),(0,l.jsx)("span",{className:"ml-2 font-medium ".concat(D?"text-green-600":"text-red-600"),children:D?"Active":"Inactive"})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[k&&(0,l.jsx)(c.$,{type:"button",variant:"outline",onClick:k,children:"Cancel"}),(0,l.jsxs)(c.$,{type:"submit",disabled:S,children:[S&&(0,l.jsx)(f.A,{className:"mr-2 h-4 w-4 animate-spin"}),A?"Update Course":"Create Course"]})]})]})})]})}},2714:(e,s,a)=>{a.d(s,{J:()=>d});var l=a(5155),r=a(2115),i=a(968),t=a(2085),n=a(3999);let c=(0,t.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)(i.b,{ref:s,className:(0,n.cn)(c(),a),...r})});d.displayName=i.b.displayName},8524:(e,s,a)=>{a.d(s,{A0:()=>n,BF:()=>c,Hj:()=>d,XI:()=>t,nA:()=>m,nd:()=>o});var l=a(5155),r=a(2115),i=a(3999);let t=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("div",{className:"relative w-full overflow-auto",children:(0,l.jsx)("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",a),...r})})});t.displayName="Table";let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",a),...r})});n.displayName="TableHeader";let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",a),...r})});c.displayName="TableBody",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...r})}).displayName="TableFooter";let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...r})});d.displayName="TableRow";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("th",{ref:s,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...r})});o.displayName="TableHead";let m=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("td",{ref:s,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...r})});m.displayName="TableCell",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsx)("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",a),...r})}).displayName="TableCaption"}}]);