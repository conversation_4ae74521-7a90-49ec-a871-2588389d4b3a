(()=>{var e={};e.id=635,e.ids=[635],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25619:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(37413),a=r(51358),n=r(99455),i=r(94592),d=r(18618);let c=(0,d.A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),o=(0,d.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var l=r(40918),x=r(53148);let u=(0,d.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var m=r(49046),h=r(75234);function p(){let e=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"upcoming":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},s=e=>{switch(e){case"completed":return"✓";case"upcoming":return"⏰";default:return"\uD83D\uDCC5"}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Schedule"}),(0,t.jsx)("p",{className:"text-gray-600",children:"View your upcoming classes and assignments"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:(0,t.jsx)(c,{className:"h-4 w-4"})}),(0,t.jsx)("span",{className:"text-sm font-medium px-4",children:"January 15-21, 2024"}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:(0,t.jsx)(o,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center",children:[(0,t.jsx)(l.A,{className:"h-5 w-5 mr-2"}),"Weekly Schedule"]}),(0,t.jsx)(a.BT,{children:"Your classes for this week"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{id:1,day:"Monday",date:"2024-01-15",time:"10:00 - 11:30",course:"General English B1",teacher:"Sarah Johnson",room:"Room 205",topic:"Present Perfect Tense",status:"completed"},{id:2,day:"Wednesday",date:"2024-01-17",time:"10:00 - 11:30",course:"General English B1",teacher:"Sarah Johnson",room:"Room 205",topic:"Vocabulary: Travel & Tourism",status:"upcoming"},{id:3,day:"Friday",date:"2024-01-19",time:"10:00 - 11:30",course:"General English B1",teacher:"Sarah Johnson",room:"Room 205",topic:"Reading Comprehension",status:"upcoming"},{id:4,day:"Monday",date:"2024-01-22",time:"10:00 - 11:30",course:"General English B1",teacher:"Sarah Johnson",room:"Room 205",topic:"Speaking Practice: Describing Places",status:"scheduled"}].map(r=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-center min-w-[80px]",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:r.day}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:r.date})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:r.time})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium",children:r.course}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:r.topic}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(u,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:r.teacher})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(m.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:r.room})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:s(r.status)}),(0,t.jsx)(n.E,{className:e(r.status),children:r.status})]})]},r.id))})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Upcoming Assignments"]}),(0,t.jsx)(a.BT,{children:"Assignments due soon"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{title:"Essay: My Dream Vacation",dueDate:"2024-01-20",course:"General English B1",status:"pending"},{title:"Vocabulary Quiz: Unit 6",dueDate:"2024-01-24",course:"General English B1",status:"pending"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.course}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,t.jsx)(x.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["Due: ",e.dueDate]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.E,{className:"bg-orange-100 text-orange-800",children:e.status}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",children:"View Details"})]})]},s))})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsx)(a.ZB,{children:"Quick Actions"})}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(i.$,{variant:"outline",className:"h-16 flex flex-col space-y-1",children:[(0,t.jsx)(l.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"text-sm",children:"Add to Calendar"})]}),(0,t.jsxs)(i.$,{variant:"outline",className:"h-16 flex flex-col space-y-1",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"text-sm",children:"Course Materials"})]}),(0,t.jsxs)(i.$,{variant:"outline",className:"h-16 flex flex-col space-y-1",children:[(0,t.jsx)(u,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"text-sm",children:"Contact Teacher"})]}),(0,t.jsxs)(i.$,{variant:"outline",className:"h-16 flex flex-col space-y-1",children:[(0,t.jsx)(x.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"text-sm",children:"Request Makeup"})]})]})})]})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40918:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},47359:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>l,routeModule:()=>u,tree:()=>o});var t=r(65239),a=r(48088),n=r(88170),i=r.n(n),d=r(30893),c={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);r.d(s,c);let o={children:["",{children:["(dashboard)",{children:["dashboard",{children:["student",{children:["schedule",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,25619)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\schedule\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\schedule\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/student/schedule/page",pathname:"/dashboard/student/schedule",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},49046:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},51358:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>l,ZB:()=>c,Zp:()=>i,aR:()=>d});var t=r(37413),a=r(61120),n=r(66819);let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));i.displayName="Card";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let c=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let l=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...s}));l.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(75986),a=r(8974);function n(...e){return(0,a.QP)((0,t.$)(e))}},75234:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},78335:()=>{},94592:(e,s,r)=>{"use strict";r.d(s,{$:()=>o});var t=r(37413),a=r(61120),n=r(70403),i=r(50662),d=r(66819);let c=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef(({className:e,variant:s,size:r,asChild:a=!1,...i},o)=>{let l=a?n.DX:"button";return(0,t.jsx)(l,{className:(0,d.cn)(c({variant:s,size:r,className:e})),ref:o,...i})});o.displayName="Button"},96487:()=>{},99455:(e,s,r)=>{"use strict";r.d(s,{E:()=>d});var t=r(37413);r(61120);var a=r(50662),n=r(66819);let i=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:s,...r}){return(0,t.jsx)("div",{className:(0,n.cn)(i({variant:s}),e),...r})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,7144,6631],()=>r(47359));module.exports=t})();