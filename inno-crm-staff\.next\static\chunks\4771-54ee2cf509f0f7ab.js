"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4771],{704:(e,t,r)=>{r.d(t,{B8:()=>F,UC:()=>T,bL:()=>D,l9:()=>I});var a=r(2115),n=r(5185),o=r(6081),l=r(9196),i=r(8905),s=r(3655),c=r(4315),u=r(5845),d=r(1285),y=r(5155),f="Tabs",[h,v]=(0,o.A)(f,[l.RG]),p=(0,l.RG)(),[k,x]=h(f),A=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:o,orientation:l="horizontal",dir:i,activationMode:h="automatic",...v}=e,p=(0,c.jH)(i),[x,A]=(0,u.i)({prop:a,onChange:n,defaultProp:null!=o?o:"",caller:f});return(0,y.jsx)(k,{scope:r,baseId:(0,d.B)(),value:x,onValueChange:A,orientation:l,dir:p,activationMode:h,children:(0,y.jsx)(s.sG.div,{dir:p,"data-orientation":l,...v,ref:t})})});A.displayName=f;var b="TabsList",m=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,o=x(b,r),i=p(r);return(0,y.jsx)(l.bL,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:a,children:(0,y.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});m.displayName=b;var w="TabsTrigger",g=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:o=!1,...i}=e,c=x(w,r),u=p(r),d=j(c.baseId,a),f=C(c.baseId,a),h=a===c.value;return(0,y.jsx)(l.q7,{asChild:!0,...u,focusable:!o,active:h,children:(0,y.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":f,"data-state":h?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:d,...i,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||o||!e||c.onValueChange(a)})})})});g.displayName=w;var M="TabsContent",R=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:l,...c}=e,u=x(M,r),d=j(u.baseId,n),f=C(u.baseId,n),h=n===u.value,v=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,y.jsx)(i.C,{present:o||h,children:r=>{let{present:a}=r;return(0,y.jsx)(s.sG.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":d,hidden:!a,id:f,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:a&&l})}})});function j(e,t){return"".concat(e,"-trigger-").concat(t)}function C(e,t){return"".concat(e,"-content-").concat(t)}R.displayName=M;var D=A,F=m,I=g,T=R},968:(e,t,r)=>{r.d(t,{b:()=>i});var a=r(2115),n=r(3655),o=r(5155),l=a.forwardRef((e,t)=>(0,o.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},1007:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1788:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2178:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]])},3904:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4186:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5690:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},6076:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("PhoneOff",[["path",{d:"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91",key:"z86iuo"}],["line",{x1:"22",x2:"2",y1:"2",y2:"22",key:"11kh81"}]])},7580:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7924:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8533:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8979:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},9022:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},9074:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9196:(e,t,r)=>{r.d(t,{RG:()=>m,bL:()=>I,q7:()=>T});var a=r(2115),n=r(5185),o=r(7328),l=r(6101),i=r(6081),s=r(1285),c=r(3655),u=r(9033),d=r(5845),y=r(4315),f=r(5155),h="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},p="RovingFocusGroup",[k,x,A]=(0,o.N)(p),[b,m]=(0,i.A)(p,[A]),[w,g]=b(p),M=a.forwardRef((e,t)=>(0,f.jsx)(k.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(k.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(R,{...e,ref:t})})}));M.displayName=p;var R=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:i=!1,dir:s,currentTabStopId:k,defaultCurrentTabStopId:A,onCurrentTabStopIdChange:b,onEntryFocus:m,preventScrollOnEntryFocus:g=!1,...M}=e,R=a.useRef(null),j=(0,l.s)(t,R),C=(0,y.jH)(s),[D,I]=(0,d.i)({prop:k,defaultProp:null!=A?A:null,onChange:b,caller:p}),[T,G]=a.useState(!1),L=(0,u.c)(m),E=x(r),q=a.useRef(!1),[P,S]=a.useState(0);return a.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(h,L),()=>e.removeEventListener(h,L)},[L]),(0,f.jsx)(w,{scope:r,orientation:o,dir:C,loop:i,currentTabStopId:D,onItemFocus:a.useCallback(e=>I(e),[I]),onItemShiftTab:a.useCallback(()=>G(!0),[]),onFocusableItemAdd:a.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>S(e=>e-1),[]),children:(0,f.jsx)(c.sG.div,{tabIndex:T||0===P?-1:0,"data-orientation":o,...M,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{q.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!q.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(h,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=E().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),g)}}q.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>G(!1))})})}),j="RovingFocusGroupItem",C=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:l=!1,tabStopId:i,children:u,...d}=e,y=(0,s.B)(),h=i||y,v=g(j,r),p=v.currentTabStopId===h,A=x(r),{onFocusableItemAdd:b,onFocusableItemRemove:m,currentTabStopId:w}=v;return a.useEffect(()=>{if(o)return b(),()=>m()},[o,b,m]),(0,f.jsx)(k.ItemSlot,{scope:r,id:h,focusable:o,active:l,children:(0,f.jsx)(c.sG.span,{tabIndex:p?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o?v.onItemFocus(h):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>v.onItemFocus(h)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return D[n]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=A().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>F(r))}}),children:"function"==typeof u?u({isCurrentTabStop:p,hasTabStop:null!=w}):u})})});C.displayName=j;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var I=M,T=C},9420:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9588:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(2895).A)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])}}]);