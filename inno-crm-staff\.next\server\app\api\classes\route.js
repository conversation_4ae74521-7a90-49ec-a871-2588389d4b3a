"use strict";(()=>{var e={};e.id=1786,e.ids=[1786],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83445:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>m,serverHooks:()=>j,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>x,POST:()=>h});var a=t(96559),o=t(48088),n=t(37719),i=t(32190),u=t(19854),d=t(41098),p=t(79464),c=t(99326),l=t(45697);let g=l.Ik({groupId:l.Yj().min(1,"Group is required"),teacherId:l.Yj().min(1,"Teacher is required"),date:l.Yj().min(1,"Date is required"),topic:l.Yj().optional(),homework:l.Yj().optional(),notes:l.Yj().optional()});async function x(e){try{let r=await (0,u.getServerSession)(d.N);if(!r?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"20"),o=t.get("groupId"),n=t.get("teacherId"),c=t.get("dateFrom"),l=t.get("dateTo"),g={};o&&(g.groupId=o),n&&(g.teacherId=n),(c||l)&&(g.date={},c&&(g.date.gte=new Date(c)),l&&(g.date.lte=new Date(l)));let[x,h]=await Promise.all([p.z.class.findMany({where:g,include:{group:{include:{course:{select:{name:!0,level:!0}}}},teacher:{include:{user:{select:{id:!0,name:!0}}}},_count:{select:{attendances:!0}}},orderBy:{date:"desc"},skip:(s-1)*a,take:a}),p.z.class.count({where:g})]);return i.NextResponse.json({classes:x,pagination:{page:s,limit:a,total:h,pages:Math.ceil(h/a)}})}catch(e){return console.error("Error fetching classes:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let r=await (0,u.getServerSession)(d.N);if(!r?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.role||!["ADMIN","MANAGER","TEACHER"].includes(r.user.role))return i.NextResponse.json({error:"Forbidden"},{status:403});let t=await e.json(),s=g.parse(t);if(!await p.z.group.findUnique({where:{id:s.groupId},include:{course:{select:{name:!0,level:!0}}}}))return i.NextResponse.json({error:"Group not found"},{status:400});if(!await p.z.teacher.findUnique({where:{id:s.teacherId},include:{user:{select:{name:!0}}}}))return i.NextResponse.json({error:"Teacher not found"},{status:400});if(await p.z.class.findFirst({where:{groupId:s.groupId,date:new Date(s.date)}}))return i.NextResponse.json({error:"A class already exists for this group on this date"},{status:400});let a=await p.z.class.create({data:{...s,date:new Date(s.date)},include:{group:{include:{course:{select:{name:!0,level:!0}}}},teacher:{include:{user:{select:{id:!0,name:!0}}}},_count:{select:{attendances:!0}}}});return await c._.log({userId:r.user.id,userRole:r.user.role,action:"CREATE",resource:"class",resourceId:a.id,details:{groupName:a.group.name,courseName:a.group.course?.name,teacherName:a.teacher.user.name,date:a.date.toISOString(),topic:a.topic},ipAddress:c._.getIpAddress(e),userAgent:c._.getUserAgent(e)}),i.NextResponse.json(a,{status:201})}catch(e){if(e instanceof l.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating class:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/classes/route",pathname:"/api/classes",filename:"route",bundlePath:"app/api/classes/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\classes\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:j}=m;function q(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(83445));module.exports=s})();