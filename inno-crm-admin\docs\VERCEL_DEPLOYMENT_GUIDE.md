# Vercel Deployment Guide for Inno CRM

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **GitHub Repository**: Your code should be pushed to GitHub
3. **Neon Database**: Already configured with the connection string

## Step 1: Environment Variables Setup

In your Vercel project dashboard, go to **Settings > Environment Variables** and add the following:

### Required Variables

```
DATABASE_URL=postgresql://crm_owner:<EMAIL>/crm?sslmode=require
NEXTAUTH_SECRET=your-production-secret-key-here-make-it-long-and-random
NEXTAUTH_URL=https://your-vercel-app-name.vercel.app
APP_NAME=Innovative Centre
APP_ENV=production
PRISMA_GENERATE_DATAPROXY=true
```

### Optional Variables (copy from .env.production)

Add all other variables from `.env.production` file as needed.

## Step 2: Deploy to Vercel

### Option A: Deploy via Vercel Dashboard

1. Go to [vercel.com/new](https://vercel.com/new)
2. Import your GitHub repository
3. Configure the project:
   - **Framework Preset**: Next.js
   - **Build Command**: `npm run build` (already configured)
   - **Install Command**: `npm install` (already configured)
4. Add environment variables (see Step 1)
5. Click **Deploy**

### Option B: Deploy via Vercel CLI

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

## Step 3: Pre-Deployment Check

Run the deployment preparation script to verify everything is ready:

```bash
npm run prepare-deployment
```

This will check:
- Required files are present
- Environment variables are configured
- Dependencies are installed
- Build scripts are properly configured

## Step 4: Post-Deployment Setup

1. **Update NEXTAUTH_URL**: Replace `your-vercel-app-name` with your actual Vercel app URL
2. **Test Database Connection**: Visit `/api/health` to check if the database is connected
3. **Test Authentication**: Try logging in with existing credentials
4. **Verify Features**: Test key CRM features

## Step 5: Domain Configuration (Optional)

1. Go to your Vercel project dashboard
2. Navigate to **Settings > Domains**
3. Add your custom domain
4. Update `NEXTAUTH_URL` and `APP_URL` environment variables

## Troubleshooting

### Common Issues

1. **Prisma Client Error**: 
   - Ensure `PRISMA_GENERATE_DATAPROXY=true` is set
   - Check that `postinstall` script runs `prisma generate`

2. **Database Connection Error**:
   - Verify `DATABASE_URL` is correct
   - Check Neon database is accessible

3. **Authentication Error**:
   - Ensure `NEXTAUTH_SECRET` is set
   - Verify `NEXTAUTH_URL` matches your deployment URL

4. **Build Timeout**:
   - Check for any infinite loops in API routes
   - Verify all dependencies are properly installed

### Build Logs

If deployment fails, check the build logs in Vercel dashboard for specific errors.

## Environment Variables Reference

Copy these to your Vercel project environment variables:

```
DATABASE_URL=postgresql://crm_owner:<EMAIL>/crm?sslmode=require
NEXTAUTH_SECRET=your-production-secret-key-here-make-it-long-and-random
NEXTAUTH_URL=https://your-vercel-app-name.vercel.app
APP_NAME=Innovative Centre
APP_ENV=production
PRISMA_GENERATE_DATAPROXY=true
SMS_PROVIDER=eskiz
EMAIL_PROVIDER=gmail
TIMEZONE=Asia/Tashkent
CURRENCY=UZS
LOCALE=uz-UZ
PHONE_COUNTRY_CODE=+998
DEFAULT_BRANCH=Main
BRANCHES=Main,Chilonzor,Yunusobod
FEATURE_SMS_ENABLED=true
FEATURE_EMAIL_ENABLED=true
FEATURE_WORKFLOWS_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_REPORTS_ENABLED=true
FEATURE_BULK_OPERATIONS=true
LOG_LEVEL=info
DEBUG=false
MOCK_SMS=false
MOCK_EMAIL=false
```

## Success Checklist

- [ ] Repository pushed to GitHub
- [ ] Vercel project created and connected to GitHub
- [ ] All required environment variables added
- [ ] Deployment successful
- [ ] Database connection working
- [ ] Authentication working
- [ ] Key features tested
- [ ] Custom domain configured (if applicable)

## Support

If you encounter issues:

1. Check Vercel build logs
2. Verify environment variables
3. Test database connectivity
4. Check API routes functionality
