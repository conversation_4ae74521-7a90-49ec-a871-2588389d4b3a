(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{1476:(e,r,t)=>{"use strict";t.d(r,{LeadForm:()=>m});var n=t(5155),i=t(2115),s=t(7705),o=t(2177),a=t(221),l=t(1153),u=t(7168),c=t(9852),d=t(2714);let f=l.Ik({name:l.Yj().min(2,"Name must be at least 2 characters"),phone:l.Yj().min(9,"Please enter a valid phone number"),coursePreference:l.Yj().min(1,"Please select a course")});function m(){let e=(0,s.Z)(),r=(null==e?void 0:e.currentBranch)||{id:"main"},[t,l]=(0,i.useState)(!1),[m,h]=(0,i.useState)(!1),{register:p,handleSubmit:g,formState:{errors:b},reset:v}=(0,o.mN)({resolver:(0,a.u)(f)}),x=async e=>{l(!0);try{if((await fetch("/api/leads",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,branch:r.id})})).ok)h(!0),v();else throw Error("Failed to submit form")}catch(e){console.error("Error submitting form:",e),alert("There was an error submitting your information. Please try again.")}finally{l(!1)}};return m?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("div",{className:"text-green-600 text-lg font-semibold mb-2",children:"Thank you for your interest!"}),(0,n.jsx)("p",{className:"text-gray-600 mb-4",children:"We'll contact you within 24 hours to discuss your English learning goals."}),(0,n.jsx)(u.$,{onClick:()=>h(!1),variant:"outline",children:"Submit Another Request"})]}):(0,n.jsxs)("form",{onSubmit:g(x),className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(d.J,{htmlFor:"name",children:"Full Name"}),(0,n.jsx)(c.p,{id:"name",...p("name"),placeholder:"Enter your full name",className:"mt-1"}),b.name&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.name.message})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(d.J,{htmlFor:"phone",children:"Phone Number"}),(0,n.jsx)(c.p,{id:"phone",...p("phone"),placeholder:"+998 90 123 45 67",className:"mt-1"}),b.phone&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.phone.message})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(d.J,{htmlFor:"coursePreference",children:"Course Interest"}),(0,n.jsxs)("select",{id:"coursePreference",...p("coursePreference"),className:"mt-1 block w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",children:[(0,n.jsx)("option",{value:"",children:"Select a course"}),(0,n.jsx)("option",{value:"General English",children:"General English"}),(0,n.jsx)("option",{value:"IELTS Preparation",children:"IELTS Preparation"}),(0,n.jsx)("option",{value:"SAT Preparation",children:"SAT Preparation"}),(0,n.jsx)("option",{value:"Kids English",children:"Kids English"}),(0,n.jsx)("option",{value:"Business English",children:"Business English"}),(0,n.jsx)("option",{value:"Math Courses",children:"Math Courses"})]}),b.coursePreference&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.coursePreference.message})]}),(0,n.jsx)(u.$,{type:"submit",className:"w-full",disabled:t,children:t?"Submitting...":"Get Free Consultation"})]})}},2714:(e,r,t)=>{"use strict";t.d(r,{J:()=>u});var n=t(5155),i=t(2115),s=t(968),o=t(2085),a=t(3999);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,n.jsx)(s.b,{ref:r,className:(0,a.cn)(l(),t),...i})});u.displayName=s.b.displayName},3655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>l,sG:()=>a});var n=t(2115),i=t(7650),s=t(9708),o=t(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,s.TL)(`Primitive.${r}`),i=n.forwardRef((e,n)=>{let{asChild:i,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?t:r,{...s,ref:n})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{});function l(e,r){e&&i.flushSync(()=>e.dispatchEvent(r))}},3999:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>a,cn:()=>s,r6:()=>l,vv:()=>o});var n=t(2596),i=t(9688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,i.QP)((0,n.$)(r))}function o(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function a(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(r)}function l(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>o,t:()=>s});var n=t(2115);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function s(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}function o(...e){return n.useCallback(s(...e),e)}},6491:(e,r,t)=>{Promise.resolve().then(t.bind(t,1476)),Promise.resolve().then(t.t.bind(t,6874,23))},7168:(e,r,t)=>{"use strict";t.d(r,{$:()=>u,r:()=>l});var n=t(5155),i=t(2115),s=t(9708),o=t(2085),a=t(3999);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=i.forwardRef((e,r)=>{let{className:t,variant:i,size:o,asChild:u=!1,...c}=e,d=u?s.DX:"button";return(0,n.jsx)(d,{className:(0,a.cn)(l({variant:i,size:o,className:t})),ref:r,...c})});u.displayName="Button"},7705:(e,r,t)=>{"use strict";t.d(r,{BranchProvider:()=>a,O:()=>l,Z:()=>u});var n=t(5155),i=t(2115);let s=(0,i.createContext)(void 0),o=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function a(e){let{children:r}=e,[t,a]=(0,i.useState)(o[0]),[l]=(0,i.useState)(o),[u,c]=(0,i.useState)(!0);return(0,i.useEffect)(()=>{let e=localStorage.getItem("selectedBranch");if(e){let r=l.find(r=>r.id===e);r&&a(r)}c(!1)},[l]),(0,n.jsx)(s.Provider,{value:{currentBranch:t,branches:l,switchBranch:e=>{let r=l.find(r=>r.id===e);r&&(a(r),localStorage.setItem("selectedBranch",e))},isLoading:u},children:r})}function l(){let e=(0,i.useContext)(s);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function u(){return(0,i.useContext)(s)||null}},9708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>a,Dc:()=>u,TL:()=>o});var n=t(2115),i=t(6101),s=t(5155);function o(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...s}=e;if(n.isValidElement(t)){var o;let e,a,l=(o=t,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,r){let t={...r};for(let n in r){let i=e[n],s=r[n];/^on[A-Z]/.test(n)?i&&s?t[n]=(...e)=>{let r=s(...e);return i(...e),r}:i&&(t[n]=i):"style"===n?t[n]={...i,...s}:"className"===n&&(t[n]=[i,s].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==n.Fragment&&(u.ref=r?(0,i.t)(r,l):l),n.cloneElement(t,u)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:i,...o}=e,a=n.Children.toArray(i),l=a.find(c);if(l){let e=l.props.children,i=a.map(r=>r!==l?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(r,{...o,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}var a=o("Slot"),l=Symbol("radix.slottable");function u(e){let r=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=l,r}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9852:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var n=t(5155),i=t(2115),s=t(3999);let o=i.forwardRef((e,r)=>{let{className:t,type:i,...o}=e;return(0,n.jsx)("input",{type:i,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...o})});o.displayName="Input"}},e=>{var r=r=>e(e.s=r);e.O(0,[5003,2356,6874,8441,1684,7358],()=>r(6491)),_N_E=e.O()}]);