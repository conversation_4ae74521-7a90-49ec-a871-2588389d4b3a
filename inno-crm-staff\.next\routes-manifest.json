{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/assessments/[id]", "regex": "^/api/assessments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/assessments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/attendance/[id]", "regex": "^/api/attendance/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/attendance/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/cabinets/[id]", "regex": "^/api/cabinets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/cabinets/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/cabinets/[id]/schedules", "regex": "^/api/cabinets/([^/]+?)/schedules(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/cabinets/(?<nxtPid>[^/]+?)/schedules(?:/)?$"}, {"page": "/api/classes/[id]", "regex": "^/api/classes/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/classes/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/courses/[id]", "regex": "^/api/courses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/courses/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/enrollments/[id]", "regex": "^/api/enrollments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/enrollments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/groups/[id]", "regex": "^/api/groups/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/groups/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/leads/[id]", "regex": "^/api/leads/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/leads/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/leads/[id]/archive", "regex": "^/api/leads/([^/]+?)/archive(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/leads/(?<nxtPid>[^/]+?)/archive(?:/)?$"}, {"page": "/api/leads/[id]/assign-group", "regex": "^/api/leads/([^/]+?)/assign\\-group(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/leads/(?<nxtPid>[^/]+?)/assign\\-group(?:/)?$"}, {"page": "/api/leads/[id]/call", "regex": "^/api/leads/([^/]+?)/call(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/leads/(?<nxtPid>[^/]+?)/call(?:/)?$"}, {"page": "/api/students/[id]", "regex": "^/api/students/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/students/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/students/[id]/assignments", "regex": "^/api/students/([^/]+?)/assignments(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/students/(?<nxtPid>[^/]+?)/assignments(?:/)?$"}, {"page": "/api/students/[id]/certificates", "regex": "^/api/students/([^/]+?)/certificates(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/students/(?<nxtPid>[^/]+?)/certificates(?:/)?$"}, {"page": "/api/students/[id]/payments", "regex": "^/api/students/([^/]+?)/payments(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/students/(?<nxtPid>[^/]+?)/payments(?:/)?$"}, {"page": "/api/students/[id]/progress", "regex": "^/api/students/([^/]+?)/progress(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/students/(?<nxtPid>[^/]+?)/progress(?:/)?$"}, {"page": "/api/students/[id]/status", "regex": "^/api/students/([^/]+?)/status(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/students/(?<nxtPid>[^/]+?)/status(?:/)?$"}, {"page": "/api/teachers/[id]", "regex": "^/api/teachers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/teachers/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/teachers/[id]/kpis", "regex": "^/api/teachers/([^/]+?)/kpis(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/teachers/(?<nxtPid>[^/]+?)/kpis(?:/)?$"}, {"page": "/api/users/[id]", "regex": "^/api/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/cabinets/[id]", "regex": "^/dashboard/cabinets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/cabinets/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/groups/[id]", "regex": "^/dashboard/groups/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/groups/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/students/[id]", "regex": "^/dashboard/students/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/students/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/teachers/[id]", "regex": "^/dashboard/teachers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/teachers/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/assessments", "regex": "^/dashboard/assessments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/assessments(?:/)?$"}, {"page": "/dashboard/attendance", "regex": "^/dashboard/attendance(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/attendance(?:/)?$"}, {"page": "/dashboard/cabinets", "regex": "^/dashboard/cabinets(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/cabinets(?:/)?$"}, {"page": "/dashboard/communication", "regex": "^/dashboard/communication(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/communication(?:/)?$"}, {"page": "/dashboard/communication/announcements", "regex": "^/dashboard/communication/announcements(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/communication/announcements(?:/)?$"}, {"page": "/dashboard/communication/messages", "regex": "^/dashboard/communication/messages(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/communication/messages(?:/)?$"}, {"page": "/dashboard/courses", "regex": "^/dashboard/courses(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/courses(?:/)?$"}, {"page": "/dashboard/enrollments", "regex": "^/dashboard/enrollments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/enrollments(?:/)?$"}, {"page": "/dashboard/groups", "regex": "^/dashboard/groups(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/groups(?:/)?$"}, {"page": "/dashboard/leads", "regex": "^/dashboard/leads(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/leads(?:/)?$"}, {"page": "/dashboard/settings", "regex": "^/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/settings(?:/)?$"}, {"page": "/dashboard/student", "regex": "^/dashboard/student(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student(?:/)?$"}, {"page": "/dashboard/student/assignments", "regex": "^/dashboard/student/assignments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/assignments(?:/)?$"}, {"page": "/dashboard/student/attendance", "regex": "^/dashboard/student/attendance(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/attendance(?:/)?$"}, {"page": "/dashboard/student/certificates", "regex": "^/dashboard/student/certificates(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/certificates(?:/)?$"}, {"page": "/dashboard/student/payments", "regex": "^/dashboard/student/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/payments(?:/)?$"}, {"page": "/dashboard/student/progress", "regex": "^/dashboard/student/progress(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/progress(?:/)?$"}, {"page": "/dashboard/student/schedule", "regex": "^/dashboard/student/schedule(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/student/schedule(?:/)?$"}, {"page": "/dashboard/students", "regex": "^/dashboard/students(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/students(?:/)?$"}, {"page": "/dashboard/teachers", "regex": "^/dashboard/teachers(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/teachers(?:/)?$"}, {"page": "/dashboard/test-notifications", "regex": "^/dashboard/test\\-notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/test\\-notifications(?:/)?$"}, {"page": "/dashboard/unauthorized", "regex": "^/dashboard/unauthorized(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/unauthorized(?:/)?$"}, {"page": "/dashboard/users", "regex": "^/dashboard/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/users(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}