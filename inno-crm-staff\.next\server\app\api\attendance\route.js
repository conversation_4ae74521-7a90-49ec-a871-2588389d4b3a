(()=>{var e={};e.id=1519,e.ids=[1519],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},78466:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>f,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var n={};s.r(n),s.d(n,{GET:()=>p,POST:()=>I});var r=s(96559),a=s(48088),d=s(37719),u=s(32190),o=s(79464),c=s(45697);let i=c.Ik({studentId:c.Yj(),classId:c.Yj(),status:c.k5(["PRESENT","ABSENT","LATE","EXCUSED"]).default("PRESENT"),notes:c.Yj().optional()}),l=c.Ik({classId:c.Yj(),attendances:c.YO(c.Ik({studentId:c.Yj(),status:c.k5(["PRESENT","ABSENT","LATE","EXCUSED"]),notes:c.Yj().optional()}))});async function p(e){try{let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"20"),r=t.get("classId"),a=t.get("studentId"),d=t.get("groupId"),c=t.get("teacherId"),i=t.get("status"),l=t.get("dateFrom"),p=t.get("dateTo"),I=t.get("branch")||"main",g={AND:[{student:{branch:I}},{class:{group:{branch:I}}}]};if(r&&(g.classId=r,g.AND.push({class:{id:r,group:{branch:I}}})),a&&(g.studentId=a,g.AND.push({student:{id:a,branch:I}})),d&&(g.class={...g.class,groupId:d,group:{branch:I}}),c){let e=await o.z.teacher.findUnique({where:{userId:c,branch:I},select:{id:!0}});e&&(g.class={...g.class,teacherId:e.id})}i&&(g.status=i),(l||p)&&(g.class={...g.class,date:{...l&&{gte:new Date(l)},...p&&{lte:new Date(p)}}});let[h,m]=await Promise.all([o.z.attendance.findMany({where:g,include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0}}}},class:{include:{group:{select:{id:!0,name:!0}},teacher:{include:{user:{select:{name:!0}}}}}}},orderBy:[{class:{date:"desc"}},{createdAt:"desc"}],skip:(s-1)*n,take:n}),o.z.attendance.count({where:g})]);return u.NextResponse.json({attendances:h,pagination:{page:s,limit:n,total:m,pages:Math.ceil(m/n)}})}catch(e){return console.error("Error fetching attendance:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function I(e){try{let t=await e.json();if(t.attendances&&Array.isArray(t.attendances)){let e=l.parse(t),s=await o.z.class.findUnique({where:{id:e.classId},include:{group:{include:{enrollments:{where:{status:"ACTIVE"},select:{studentId:!0}}}}}});if(!s)return u.NextResponse.json({error:"Class not found"},{status:400});let n=s.group.enrollments.map(e=>e.studentId),r=e.attendances.filter(e=>!n.includes(e.studentId));if(r.length>0)return u.NextResponse.json({error:"Some students are not enrolled in this group",invalidStudents:r.map(e=>e.studentId)},{status:400});let a=await Promise.all(e.attendances.map(t=>o.z.attendance.upsert({where:{studentId_classId:{studentId:t.studentId,classId:e.classId}},update:{status:t.status,notes:t.notes,updatedAt:new Date},create:{studentId:t.studentId,classId:e.classId,status:t.status,notes:t.notes},include:{student:{include:{user:{select:{name:!0}}}}}})));return u.NextResponse.json({message:"Bulk attendance recorded successfully",attendances:a},{status:201})}{let e=i.parse(t);if(!await o.z.enrollment.findFirst({where:{studentId:e.studentId,status:"ACTIVE",group:{classes:{some:{id:e.classId}}}}}))return u.NextResponse.json({error:"Student is not enrolled in this class group"},{status:400});if(await o.z.attendance.findUnique({where:{studentId_classId:{studentId:e.studentId,classId:e.classId}}}))return u.NextResponse.json({error:"Attendance already recorded for this student in this class"},{status:400});let s=await o.z.attendance.create({data:e,include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0}}}},class:{include:{group:{select:{name:!0}}}}}});return u.NextResponse.json(s,{status:201})}}catch(e){if(e instanceof c.G)return u.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating attendance:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new r.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/attendance/route",pathname:"/api/attendance",filename:"route",bundlePath:"app/api/attendance/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\attendance\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:f}=g;function x(){return(0,d.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},79464:(e,t,s)=>{"use strict";s.d(t,{z:()=>r});var n=s(96330);let r=globalThis.prisma??new n.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[4243,580,5697],()=>s(78466));module.exports=n})();