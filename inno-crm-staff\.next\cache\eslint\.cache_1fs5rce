[{"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\assessments\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\attendance\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\cabinets\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\cabinets\\[id]\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\announcements\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\messages\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\courses\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\enrollments\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\[id]\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\leads\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\settings\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\assignments\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\attendance\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\certificates\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\payments\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\progress\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\schedule\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\[id]\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\page.tsx": "24", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\[id]\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\test-notifications\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\unauthorized\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\users\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx": "29", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\announcements\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\assessments\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\assessments\\[id]\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\attendance\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\attendance\\[id]\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\auth\\verify\\route.ts": "35", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\auth\\[...nextauth]\\route.ts": "36", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\cabinets\\route.ts": "37", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\cabinets\\[id]\\route.ts": "38", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\cabinets\\[id]\\schedules\\route.ts": "39", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\classes\\route.ts": "40", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\classes\\[id]\\route.ts": "41", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\communication\\stats\\route.ts": "42", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\courses\\route.ts": "43", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\courses\\[id]\\route.ts": "44", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\dashboard\\stats\\route.ts": "45", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\enrollments\\route.ts": "46", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\enrollments\\[id]\\route.ts": "47", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\groups\\route.ts": "48", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\groups\\[id]\\route.ts": "49", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\health\\route.ts": "50", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\inter-server\\auth\\validate\\route.ts": "51", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\inter-server\\health\\route.ts": "52", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\inter-server\\sync\\route.ts": "53", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\cleanup\\route.ts": "54", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\route.ts": "55", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\archive\\route.ts": "56", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\assign-group\\route.ts": "57", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\call\\route.ts": "58", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\route.ts": "59", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\messages\\route.ts": "60", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\notifications\\route.ts": "61", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\notifications\\test\\route.ts": "62", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\current\\dashboard\\route.ts": "63", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\current\\progress\\route.ts": "64", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\dropped\\route.ts": "65", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\route.ts": "66", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\assignments\\route.ts": "67", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\certificates\\route.ts": "68", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\payments\\route.ts": "69", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\progress\\route.ts": "70", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\route.ts": "71", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\status\\route.ts": "72", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\teachers\\route.ts": "73", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\teachers\\[id]\\kpis\\route.ts": "74", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\teachers\\[id]\\route.ts": "75", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\users\\route.ts": "76", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\users\\[id]\\route.ts": "77", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\workflows\\route.ts": "78", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\auth\\signin\\page.tsx": "79", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx": "80", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\page.tsx": "81", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\charts\\attendance-chart.tsx": "82", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\charts\\enrollment-chart.tsx": "83", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\charts\\revenue-chart.tsx": "84", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\charts\\student-progress-chart.tsx": "85", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\dashboard\\activity-feed.tsx": "86", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\dashboard\\header.tsx": "87", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\dashboard\\sidebar.tsx": "88", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\dialogs\\delete-user-dialog.tsx": "89", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\attendance-form.tsx": "90", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\cabinet-form.tsx": "91", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\cabinet-schedule-form.tsx": "92", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\course-form.tsx": "93", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\enrollment-form.tsx": "94", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\group-form.tsx": "95", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\lead-form.tsx": "96", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\payment-form.tsx": "97", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\student-form.tsx": "98", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\teacher-form.tsx": "99", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\user-form.tsx": "100", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\groups\\courses-tab.tsx": "101", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\leads\\call-manager.tsx": "102", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\leads\\date-filter.tsx": "103", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\leads\\group-assignment-modal.tsx": "104", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\leads\\leads-list.tsx": "105", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\notifications\\notification-dropdown.tsx": "106", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\providers\\auth-provider.tsx": "107", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\providers\\query-provider.tsx": "108", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\tables\\attendance-table.tsx": "109", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\tables\\enrollments-table.tsx": "110", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\tables\\payments-table.tsx": "111", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\tables\\teachers-table.tsx": "112", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\alert-dialog.tsx": "113", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\alert.tsx": "114", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\badge.tsx": "115", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\branch-switcher.tsx": "116", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\button.tsx": "117", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\card.tsx": "118", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\checkbox.tsx": "119", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\command.tsx": "120", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\dialog.tsx": "121", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\dropdown-menu.tsx": "122", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\input.tsx": "123", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\label.tsx": "124", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\popover.tsx": "125", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\progress.tsx": "126", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\scroll-area.tsx": "127", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\select.tsx": "128", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\separator.tsx": "129", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\switch.tsx": "130", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\table.tsx": "131", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\tabs.tsx": "132", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\textarea.tsx": "133", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\toast.tsx": "134", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\toaster.tsx": "135", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\activity-logger.ts": "136", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\activity-utils.ts": "137", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\auth.ts": "138", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\email.ts": "139", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\inter-server.ts": "140", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\notifications.ts": "141", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\performance.ts": "142", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\prisma.ts": "143", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\sms.ts": "144", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\utils.ts": "145", "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\workflows.ts": "146"}, {"size": 23427, "mtime": 1751000071052, "results": "147", "hashOfConfig": "148"}, {"size": 17673, "mtime": 1751000071053, "results": "149", "hashOfConfig": "148"}, {"size": 12608, "mtime": 1751000071053, "results": "150", "hashOfConfig": "148"}, {"size": 13471, "mtime": 1751000071053, "results": "151", "hashOfConfig": "148"}, {"size": 19153, "mtime": 1751000071057, "results": "152", "hashOfConfig": "148"}, {"size": 20964, "mtime": 1751000071057, "results": "153", "hashOfConfig": "148"}, {"size": 21702, "mtime": 1751000071057, "results": "154", "hashOfConfig": "148"}, {"size": 15508, "mtime": 1751000071057, "results": "155", "hashOfConfig": "148"}, {"size": 16177, "mtime": 1751000071057, "results": "156", "hashOfConfig": "148"}, {"size": 43839, "mtime": 1751000071065, "results": "157", "hashOfConfig": "148"}, {"size": 13070, "mtime": 1751000071057, "results": "158", "hashOfConfig": "148"}, {"size": 7026, "mtime": 1751000071065, "results": "159", "hashOfConfig": "148"}, {"size": 13075, "mtime": 1751000071065, "results": "160", "hashOfConfig": "148"}, {"size": 15746, "mtime": 1751000071065, "results": "161", "hashOfConfig": "148"}, {"size": 15006, "mtime": 1751000071065, "results": "162", "hashOfConfig": "148"}, {"size": 11127, "mtime": 1751000071065, "results": "163", "hashOfConfig": "148"}, {"size": 12230, "mtime": 1751000071073, "results": "164", "hashOfConfig": "148"}, {"size": 11062, "mtime": 1751000071073, "results": "165", "hashOfConfig": "148"}, {"size": 10453, "mtime": 1751000071073, "results": "166", "hashOfConfig": "148"}, {"size": 10561, "mtime": 1751000071073, "results": "167", "hashOfConfig": "148"}, {"size": 8453, "mtime": 1751000071073, "results": "168", "hashOfConfig": "148"}, {"size": 27226, "mtime": 1751000071073, "results": "169", "hashOfConfig": "148"}, {"size": 14942, "mtime": 1751000071073, "results": "170", "hashOfConfig": "148"}, {"size": 18093, "mtime": 1751000071082, "results": "171", "hashOfConfig": "148"}, {"size": 14775, "mtime": 1751000071073, "results": "172", "hashOfConfig": "148"}, {"size": 2011, "mtime": 1751000071082, "results": "173", "hashOfConfig": "148"}, {"size": 4472, "mtime": 1751000071082, "results": "174", "hashOfConfig": "148"}, {"size": 16390, "mtime": 1751000071082, "results": "175", "hashOfConfig": "148"}, {"size": 919, "mtime": 1751000071082, "results": "176", "hashOfConfig": "148"}, {"size": 4206, "mtime": 1751000071093, "results": "177", "hashOfConfig": "148"}, {"size": 8546, "mtime": 1751000071093, "results": "178", "hashOfConfig": "148"}, {"size": 7005, "mtime": 1751000071093, "results": "179", "hashOfConfig": "148"}, {"size": 8965, "mtime": 1751000071097, "results": "180", "hashOfConfig": "148"}, {"size": 7528, "mtime": 1751000071097, "results": "181", "hashOfConfig": "148"}, {"size": 2920, "mtime": 1751008859608, "results": "182", "hashOfConfig": "148"}, {"size": 176, "mtime": 1751000071097, "results": "183", "hashOfConfig": "148"}, {"size": 5515, "mtime": 1751000071108, "results": "184", "hashOfConfig": "148"}, {"size": 7821, "mtime": 1751000071097, "results": "185", "hashOfConfig": "148"}, {"size": 6227, "mtime": 1751000071106, "results": "186", "hashOfConfig": "148"}, {"size": 6299, "mtime": 1751000071110, "results": "187", "hashOfConfig": "148"}, {"size": 9453, "mtime": 1751000071109, "results": "188", "hashOfConfig": "148"}, {"size": 8218, "mtime": 1751000071113, "results": "189", "hashOfConfig": "148"}, {"size": 2926, "mtime": 1751000071116, "results": "190", "hashOfConfig": "148"}, {"size": 5285, "mtime": 1751000071115, "results": "191", "hashOfConfig": "148"}, {"size": 6637, "mtime": 1751000071121, "results": "192", "hashOfConfig": "148"}, {"size": 7002, "mtime": 1751000071126, "results": "193", "hashOfConfig": "148"}, {"size": 6630, "mtime": 1751000071125, "results": "194", "hashOfConfig": "148"}, {"size": 8479, "mtime": 1751000071130, "results": "195", "hashOfConfig": "148"}, {"size": 6427, "mtime": 1751000071129, "results": "196", "hashOfConfig": "148"}, {"size": 1180, "mtime": 1751000071132, "results": "197", "hashOfConfig": "148"}, {"size": 3168, "mtime": 1751016134279, "results": "198", "hashOfConfig": "148"}, {"size": 2588, "mtime": 1751006095276, "results": "199", "hashOfConfig": "148"}, {"size": 5475, "mtime": 1751006134528, "results": "200", "hashOfConfig": "148"}, {"size": 4345, "mtime": 1751000071137, "results": "201", "hashOfConfig": "148"}, {"size": 4806, "mtime": 1751000071137, "results": "202", "hashOfConfig": "148"}, {"size": 4804, "mtime": 1751000071137, "results": "203", "hashOfConfig": "148"}, {"size": 10772, "mtime": 1751000071137, "results": "204", "hashOfConfig": "148"}, {"size": 5210, "mtime": 1751000071137, "results": "205", "hashOfConfig": "148"}, {"size": 5111, "mtime": 1751000071137, "results": "206", "hashOfConfig": "148"}, {"size": 4186, "mtime": 1751000071145, "results": "207", "hashOfConfig": "148"}, {"size": 8759, "mtime": 1751000071145, "results": "208", "hashOfConfig": "148"}, {"size": 4120, "mtime": 1751000071150, "results": "209", "hashOfConfig": "148"}, {"size": 3057, "mtime": 1751000071167, "results": "210", "hashOfConfig": "148"}, {"size": 5636, "mtime": 1751000071167, "results": "211", "hashOfConfig": "148"}, {"size": 5822, "mtime": 1751000071167, "results": "212", "hashOfConfig": "148"}, {"size": 6096, "mtime": 1751000071177, "results": "213", "hashOfConfig": "148"}, {"size": 7063, "mtime": 1751000071153, "results": "214", "hashOfConfig": "148"}, {"size": 6558, "mtime": 1751000071153, "results": "215", "hashOfConfig": "148"}, {"size": 1227, "mtime": 1751000071161, "results": "216", "hashOfConfig": "148"}, {"size": 9295, "mtime": 1751000071161, "results": "217", "hashOfConfig": "148"}, {"size": 5555, "mtime": 1751000071167, "results": "218", "hashOfConfig": "148"}, {"size": 3676, "mtime": 1751000071167, "results": "219", "hashOfConfig": "148"}, {"size": 4860, "mtime": 1751000071177, "results": "220", "hashOfConfig": "148"}, {"size": 5341, "mtime": 1751000071177, "results": "221", "hashOfConfig": "148"}, {"size": 5579, "mtime": 1751000071177, "results": "222", "hashOfConfig": "148"}, {"size": 10308, "mtime": 1751000071177, "results": "223", "hashOfConfig": "148"}, {"size": 9608, "mtime": 1751000071177, "results": "224", "hashOfConfig": "148"}, {"size": 5188, "mtime": 1751000071177, "results": "225", "hashOfConfig": "148"}, {"size": 8686, "mtime": 1751000071177, "results": "226", "hashOfConfig": "148"}, {"size": 757, "mtime": 1751000071192, "results": "227", "hashOfConfig": "148"}, {"size": 8234, "mtime": 1751000071193, "results": "228", "hashOfConfig": "148"}, {"size": 15579, "mtime": 1751000071193, "results": "229", "hashOfConfig": "148"}, {"size": 15058, "mtime": 1751000071193, "results": "230", "hashOfConfig": "148"}, {"size": 11492, "mtime": 1751000071193, "results": "231", "hashOfConfig": "148"}, {"size": 19268, "mtime": 1751000071193, "results": "232", "hashOfConfig": "148"}, {"size": 8145, "mtime": 1751000071193, "results": "233", "hashOfConfig": "148"}, {"size": 3066, "mtime": 1751000071193, "results": "234", "hashOfConfig": "148"}, {"size": 12204, "mtime": 1751000071193, "results": "235", "hashOfConfig": "148"}, {"size": 5003, "mtime": 1751000071193, "results": "236", "hashOfConfig": "148"}, {"size": 17709, "mtime": 1751000071193, "results": "237", "hashOfConfig": "148"}, {"size": 9176, "mtime": 1751000071193, "results": "238", "hashOfConfig": "148"}, {"size": 7814, "mtime": 1751000071193, "results": "239", "hashOfConfig": "148"}, {"size": 12862, "mtime": 1751000071193, "results": "240", "hashOfConfig": "148"}, {"size": 18095, "mtime": 1751000071193, "results": "241", "hashOfConfig": "148"}, {"size": 20143, "mtime": 1751000071208, "results": "242", "hashOfConfig": "148"}, {"size": 4369, "mtime": 1751000071209, "results": "243", "hashOfConfig": "148"}, {"size": 22780, "mtime": 1751000071209, "results": "244", "hashOfConfig": "148"}, {"size": 11750, "mtime": 1751000071209, "results": "245", "hashOfConfig": "148"}, {"size": 13774, "mtime": 1751000071209, "results": "246", "hashOfConfig": "148"}, {"size": 12959, "mtime": 1751000071209, "results": "247", "hashOfConfig": "148"}, {"size": 12481, "mtime": 1751000071215, "results": "248", "hashOfConfig": "148"}, {"size": 8645, "mtime": 1751000071217, "results": "249", "hashOfConfig": "148"}, {"size": 5850, "mtime": 1751000071217, "results": "250", "hashOfConfig": "148"}, {"size": 15273, "mtime": 1751000071219, "results": "251", "hashOfConfig": "148"}, {"size": 14086, "mtime": 1751000071219, "results": "252", "hashOfConfig": "148"}, {"size": 7362, "mtime": 1751000071219, "results": "253", "hashOfConfig": "148"}, {"size": 205, "mtime": 1751000071219, "results": "254", "hashOfConfig": "148"}, {"size": 516, "mtime": 1751000071219, "results": "255", "hashOfConfig": "148"}, {"size": 15883, "mtime": 1751000071224, "results": "256", "hashOfConfig": "148"}, {"size": 17107, "mtime": 1751000071224, "results": "257", "hashOfConfig": "148"}, {"size": 15330, "mtime": 1751000071224, "results": "258", "hashOfConfig": "148"}, {"size": 12146, "mtime": 1751000071224, "results": "259", "hashOfConfig": "148"}, {"size": 4596, "mtime": 1751000071224, "results": "260", "hashOfConfig": "148"}, {"size": 1643, "mtime": 1751000071224, "results": "261", "hashOfConfig": "148"}, {"size": 1304, "mtime": 1751000071224, "results": "262", "hashOfConfig": "148"}, {"size": 3379, "mtime": 1751000071224, "results": "263", "hashOfConfig": "148"}, {"size": 2190, "mtime": 1751000071224, "results": "264", "hashOfConfig": "148"}, {"size": 2017, "mtime": 1751000071224, "results": "265", "hashOfConfig": "148"}, {"size": 1084, "mtime": 1751000071232, "results": "266", "hashOfConfig": "148"}, {"size": 5057, "mtime": 1751000071234, "results": "267", "hashOfConfig": "148"}, {"size": 4063, "mtime": 1751000071236, "results": "268", "hashOfConfig": "148"}, {"size": 7509, "mtime": 1751000071238, "results": "269", "hashOfConfig": "148"}, {"size": 849, "mtime": 1751000071239, "results": "270", "hashOfConfig": "148"}, {"size": 734, "mtime": 1751000071240, "results": "271", "hashOfConfig": "148"}, {"size": 1275, "mtime": 1751000071241, "results": "272", "hashOfConfig": "148"}, {"size": 820, "mtime": 1751000071242, "results": "273", "hashOfConfig": "148"}, {"size": 1688, "mtime": 1751000071243, "results": "274", "hashOfConfig": "148"}, {"size": 5773, "mtime": 1751000071243, "results": "275", "hashOfConfig": "148"}, {"size": 801, "mtime": 1751000071245, "results": "276", "hashOfConfig": "148"}, {"size": 1166, "mtime": 1751000071245, "results": "277", "hashOfConfig": "148"}, {"size": 2882, "mtime": 1751000071246, "results": "278", "hashOfConfig": "148"}, {"size": 1936, "mtime": 1751000071246, "results": "279", "hashOfConfig": "148"}, {"size": 796, "mtime": 1751000071248, "results": "280", "hashOfConfig": "148"}, {"size": 5371, "mtime": 1751000071248, "results": "281", "hashOfConfig": "148"}, {"size": 821, "mtime": 1751000071248, "results": "282", "hashOfConfig": "148"}, {"size": 9925, "mtime": 1751000071272, "results": "283", "hashOfConfig": "148"}, {"size": 9176, "mtime": 1751000071272, "results": "284", "hashOfConfig": "148"}, {"size": 2255, "mtime": 1751015780592, "results": "285", "hashOfConfig": "148"}, {"size": 12000, "mtime": 1751000071272, "results": "286", "hashOfConfig": "148"}, {"size": 6776, "mtime": 1751019216989, "results": "287", "hashOfConfig": "148"}, {"size": 10835, "mtime": 1751000071272, "results": "288", "hashOfConfig": "148"}, {"size": 10898, "mtime": 1751000071272, "results": "289", "hashOfConfig": "148"}, {"size": 288, "mtime": 1751000071272, "results": "290", "hashOfConfig": "148"}, {"size": 7779, "mtime": 1751000071272, "results": "291", "hashOfConfig": "148"}, {"size": 1358, "mtime": 1751000071272, "results": "292", "hashOfConfig": "148"}, {"size": 12649, "mtime": 1751000071272, "results": "293", "hashOfConfig": "148"}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "bc4m0j", {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\assessments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\attendance\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\cabinets\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\cabinets\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\announcements\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\messages\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\courses\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\enrollments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\page.tsx", ["732"], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\leads\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\assignments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\attendance\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\certificates\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\payments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\progress\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\schedule\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\page.tsx", [], ["733"], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\page.tsx", ["734"], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\test-notifications\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\unauthorized\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\announcements\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\assessments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\assessments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\attendance\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\attendance\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\auth\\verify\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\cabinets\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\cabinets\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\cabinets\\[id]\\schedules\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\classes\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\classes\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\communication\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\courses\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\courses\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\dashboard\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\enrollments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\enrollments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\groups\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\groups\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\inter-server\\auth\\validate\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\inter-server\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\inter-server\\sync\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\cleanup\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\archive\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\assign-group\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\call\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\messages\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\notifications\\test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\current\\dashboard\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\current\\progress\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\dropped\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\assignments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\certificates\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\progress\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\teachers\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\teachers\\[id]\\kpis\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\teachers\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\users\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\workflows\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\charts\\attendance-chart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\charts\\enrollment-chart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\charts\\revenue-chart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\charts\\student-progress-chart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\dashboard\\activity-feed.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\dashboard\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\dashboard\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\dialogs\\delete-user-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\attendance-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\cabinet-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\cabinet-schedule-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\course-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\enrollment-form.tsx", ["735"], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\group-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\lead-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\payment-form.tsx", ["736", "737"], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\student-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\teacher-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\forms\\user-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\groups\\courses-tab.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\leads\\call-manager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\leads\\date-filter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\leads\\group-assignment-modal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\leads\\leads-list.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\notifications\\notification-dropdown.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\providers\\auth-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\providers\\query-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\tables\\attendance-table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\tables\\enrollments-table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\tables\\payments-table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\tables\\teachers-table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\branch-switcher.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\activity-logger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\activity-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\email.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\inter-server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\notifications.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\performance.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\sms.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\lib\\workflows.ts", [], [], {"ruleId": "738", "severity": 1, "message": "739", "line": 311, "column": 6, "nodeType": "740", "endLine": 311, "endColumn": 25, "suggestions": "741"}, {"ruleId": "738", "severity": 1, "message": "742", "line": 97, "column": 6, "nodeType": "740", "endLine": 97, "endColumn": 75, "suggestions": "743", "suppressions": "744"}, {"ruleId": "738", "severity": 1, "message": "745", "line": 69, "column": 6, "nodeType": "740", "endLine": 69, "endColumn": 25, "suggestions": "746"}, {"ruleId": "738", "severity": 1, "message": "747", "line": 132, "column": 6, "nodeType": "740", "endLine": 132, "endColumn": 25, "suggestions": "748"}, {"ruleId": "738", "severity": 1, "message": "742", "line": 135, "column": 6, "nodeType": "740", "endLine": 135, "endColumn": 25, "suggestions": "749"}, {"ruleId": "738", "severity": 1, "message": "750", "line": 145, "column": 6, "nodeType": "740", "endLine": 145, "endColumn": 35, "suggestions": "751"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchGroups'. Either include it or remove the dependency array.", "ArrayExpression", ["752"], "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["753"], ["754"], "React Hook useEffect has missing dependencies: 'fetchKPIs' and 'fetchTeachers'. Either include them or remove the dependency array.", ["755"], "React Hook useEffect has missing dependencies: 'fetchGroups' and 'fetchStudents'. Either include them or remove the dependency array.", ["756"], ["757"], "React Hook useEffect has a missing dependency: 'fetchStudentPaymentHistory'. Either include it or remove the dependency array.", ["758"], {"desc": "759", "fix": "760"}, {"desc": "761", "fix": "762"}, {"kind": "763", "justification": "764"}, {"desc": "765", "fix": "766"}, {"desc": "767", "fix": "768"}, {"desc": "769", "fix": "770"}, {"desc": "771", "fix": "772"}, "Update the dependencies array to be: [currentBranch?.id, fetchGroups]", {"range": "773", "text": "774"}, "Update the dependencies array to be: [statusFilter, paymentFilter, showDroppedStudents, currentBranch?.id, fetchStudents]", {"range": "775", "text": "776"}, "directive", "", "Update the dependencies array to be: [currentBranch?.id, fetchKPIs, fetchTeachers]", {"range": "777", "text": "778"}, "Update the dependencies array to be: [currentBranch?.id, fetchGroups, fetchStudents]", {"range": "779", "text": "780"}, "Update the dependencies array to be: [currentBranch?.id, fetchStudents]", {"range": "781", "text": "782"}, "Update the dependencies array to be: [fetchStudentPaymentHistory, selectedStudentId, students]", {"range": "783", "text": "784"}, [11268, 11287], "[currentBranch?.id, fetchGroups]", [3156, 3225], "[statusFilter, paymentFilter, showDroppedStudents, currentBranch?.id, fetchStudents]", [2232, 2251], "[currentBranch?.id, fetchKPIs, fetchTeachers]", [3927, 3946], "[currentBranch?.id, fetchGroups, fetchStudents]", [4353, 4372], "[currentBranch?.id, fetchStudents]", [4639, 4668], "[fetchStudentPaymentHistory, selectedStudentId, students]"]