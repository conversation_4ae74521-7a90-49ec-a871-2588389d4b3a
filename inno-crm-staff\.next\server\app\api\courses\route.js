(()=>{var e={};e.id=8648,e.ids=[8648],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},97306:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>d});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(79464),c=t(45697);let p=c.Ik({name:c.Yj().min(1),level:c.k5(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]),description:c.Yj().optional(),duration:c.ai().min(1),price:c.ai().min(0),isActive:c.zM().default(!0)});async function l(e){try{let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"20"),n=r.get("search"),o=r.get("level"),i=r.get("isActive"),c={};n&&(c.OR=[{name:{contains:n,mode:"insensitive"}},{description:{contains:n,mode:"insensitive"}}]),o&&(c.level=o),null!==i&&(c.isActive="true"===i);let[p,l]=await Promise.all([u.z.course.findMany({where:c,include:{groups:{select:{id:!0,name:!0,_count:{select:{enrollments:!0}}}},_count:{select:{groups:!0}}},orderBy:{createdAt:"desc"},skip:(t-1)*s,take:s}),u.z.course.count({where:c})]);return a.NextResponse.json({courses:p,pagination:{page:t,limit:s,total:l,pages:Math.ceil(l/s)}})}catch(e){return console.error("Error fetching courses:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let r=await e.json(),t=p.parse(r),s=await u.z.course.create({data:t,include:{_count:{select:{groups:!0}}}});return a.NextResponse.json(s,{status:201})}catch(e){if(e instanceof c.G)return a.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating course:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/courses/route",pathname:"/api/courses",filename:"route",bundlePath:"app/api/courses/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\courses\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:g,serverHooks:x}=m;function f(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:g})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697],()=>t(97306));module.exports=s})();