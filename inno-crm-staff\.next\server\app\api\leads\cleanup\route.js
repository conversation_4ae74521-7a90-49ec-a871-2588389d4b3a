"use strict";(()=>{var e={};e.id=9578,e.ids=[9578],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69185:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>x,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{GET:()=>h,POST:()=>c});var s=t(96559),n=t(48088),o=t(37719),d=t(32190),i=t(19854),l=t(41098),u=t(79464),p=t(99326);async function c(e){try{let r=await (0,i.getServerSession)(l.N);if(!r?.user||"ADMIN"!==r.user.role)return d.NextResponse.json({error:"Unauthorized"},{status:401});let t=new Date;t.setDate(t.getDate()-30);let a=await u.z.lead.findMany({where:{archivedAt:{lt:t}},select:{id:!0,name:!0,phone:!0,archivedAt:!0}});if(0===a.length)return d.NextResponse.json({message:"No leads found for cleanup",deletedCount:0});let s=await u.z.callRecord.deleteMany({where:{leadId:{in:a.map(e=>e.id)}}}),n=await u.z.lead.deleteMany({where:{id:{in:a.map(e=>e.id)}}});return await p._.logLeadContacted(r.user.id,r.user.role,"SYSTEM",{leadName:"SYSTEM_CLEANUP",leadPhone:"SYSTEM",previousStatus:"ARCHIVED",newStatus:"DELETED",notes:`Automatic cleanup: Deleted ${n.count} archived leads older than 30 days and ${s.count} associated call records`},e),d.NextResponse.json({message:"Cleanup completed successfully",deletedLeads:n.count,deletedCallRecords:s.count,leadsDeleted:a.map(e=>({id:e.id,name:e.name,phone:e.phone,archivedAt:e.archivedAt}))})}catch(e){return console.error("Error during cleanup:",e),d.NextResponse.json({error:"Internal server error during cleanup"},{status:500})}}async function h(e){try{let e=await (0,i.getServerSession)(l.N);if(!e?.user||"ADMIN"!==e.user.role)return d.NextResponse.json({error:"Unauthorized"},{status:401});let r=new Date;r.setDate(r.getDate()-30);let t=await u.z.lead.findMany({where:{archivedAt:{lt:r}},include:{assignedGroup:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},callRecords:!0},orderBy:{archivedAt:"asc"}}),a=t.reduce((e,r)=>e+r.callRecords.length,0);return d.NextResponse.json({leadsToDelete:t.length,callRecordsToDelete:a,cutoffDate:r,leads:t.map(e=>({id:e.id,name:e.name,phone:e.phone,archivedAt:e.archivedAt,assignedGroup:e.assignedGroup?.name,callRecordsCount:e.callRecords.length}))})}catch(e){return console.error("Error getting cleanup preview:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/leads/cleanup/route",pathname:"/api/leads/cleanup",filename:"route",bundlePath:"app/api/leads/cleanup/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\leads\\cleanup\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:v,serverHooks:g}=x;function w(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:v})}},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4243,580,3412,1971],()=>t(69185));module.exports=a})();