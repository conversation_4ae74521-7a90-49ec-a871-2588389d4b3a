# Teacher Salary Calculation System

## Overview
The teacher salary system has been updated to calculate salaries based on 50% of student payments minus refunds. This creates a performance-based compensation model where teachers earn based on their students' payment activity.

## Key Features

### 1. KPI Cards on Teachers Page
- **Total Teachers**: Number of active teaching staff
- **Total Groups**: Number of active teaching groups
- **Total Classes**: Total classes conducted
- **Average Salary**: Average calculated salary (UZS format)

### 2. Salary Calculation Formula
```
Teacher Salary = (Total Student Payments - Total Refunds) × 50%
```

### 3. Payment Status Integration
- **PAID**: Payments count towards teacher salary
- **REFUNDED**: Refunds are deducted 100% from teacher earnings
- **PENDING/OVERDUE**: Do not count towards salary until paid

## Implementation Details

### Database Changes
- Teacher salaries are now calculated dynamically
- Payment status tracking includes REFUNDED status
- Real-time calculation based on current student payments

### API Endpoints

#### `/api/teachers/kpis`
- Returns overall teacher KPIs
- Calculates average salary across all teachers
- Updates teacher salary records in database

#### `/api/teachers` (Enhanced)
- Now includes payment data for each teacher
- Calculates individual teacher salaries
- Returns payment breakdown (total payments, refunds, net payments)

### UI Updates

#### Teachers Page
- KPI cards at the top showing key metrics
- Enhanced salary column showing calculation details
- Performance-based salary description

#### Salary Display
- Shows calculated salary amount
- Displays "50% of UZS X net" breakdown
- Real-time calculation based on student payments

## Business Logic

### Student-Teacher Relationship
1. **Direct Assignment**: Students assigned to groups via `currentGroupId`
2. **Historical Enrollments**: Past enrollments also count for salary calculation
3. **Payment Tracking**: All payments from teacher's students contribute to salary

### Refund Handling
- Refunds are deducted 100% from teacher earnings
- This ensures teachers are not penalized for legitimate refunds
- Net payment calculation: `Total Payments - Total Refunds`

### Real-time Updates
- Salaries recalculate when payments are made/refunded
- KPI dashboard updates automatically
- No manual salary setting required

## Usage Examples

### Example 1: Teacher with Active Students
- Teacher has 10 students
- Students paid total: UZS 5,000,000
- No refunds
- Teacher salary: UZS 2,500,000 (50% of 5,000,000)

### Example 2: Teacher with Refunds
- Teacher has 8 students
- Students paid total: UZS 4,000,000
- Refunds issued: UZS 500,000
- Net payments: UZS 3,500,000
- Teacher salary: UZS 1,750,000 (50% of 3,500,000)

### Example 3: New Teacher
- Teacher has 2 students
- Students paid total: UZS 1,000,000
- No refunds
- Teacher salary: UZS 500,000 (50% of 1,000,000)

## Benefits

### For Management
- Performance-based compensation
- Automatic salary calculation
- Real-time financial tracking
- Incentivizes student retention

### For Teachers
- Direct correlation between performance and pay
- Transparent salary calculation
- Motivation to maintain student satisfaction
- Clear payment breakdown

### For System
- Automated payroll processing
- Reduced manual calculations
- Accurate financial reporting
- Integrated payment tracking

## Technical Notes

### Performance Considerations
- Salary calculations are cached in teacher records
- KPI endpoint updates salaries periodically
- Efficient database queries with proper indexing

### Data Integrity
- Payment status validation
- Refund tracking and verification
- Historical data preservation
- Audit trail for salary changes

### Future Enhancements
- Salary calculation history
- Performance bonuses
- Tiered commission rates
- Advanced reporting features

## Configuration

### Environment Variables
No additional environment variables required.

### Database Schema
- Enhanced Teacher model with calculated salary
- Payment status includes REFUNDED
- Student-Group relationships support salary calculation

### API Rate Limits
- KPI endpoint: Recommended to call every 5 minutes
- Teacher list: Standard pagination applies
- Real-time updates via payment webhooks

## Monitoring

### Key Metrics to Track
- Average teacher salary trends
- Payment-to-salary ratios
- Refund impact on salaries
- Teacher performance correlation

### Alerts
- Unusual refund patterns
- Salary calculation errors
- Payment processing issues
- Teacher performance drops

## Support

### Common Issues
1. **Salary not updating**: Check payment status and refund records
2. **Incorrect calculations**: Verify student-teacher assignments
3. **Missing payments**: Ensure payment status is PAID
4. **Refund handling**: Confirm refund status is properly set

### Troubleshooting
- Check API logs for calculation errors
- Verify database relationships
- Validate payment data integrity
- Review teacher-student assignments

This system provides a fair, transparent, and automated approach to teacher compensation based on actual student payment performance.
