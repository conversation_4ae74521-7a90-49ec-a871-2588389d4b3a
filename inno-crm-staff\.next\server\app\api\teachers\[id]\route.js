(()=>{var e={};e.id=9607,e.ids=[9607],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26535:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{DELETE:()=>h,GET:()=>d,PUT:()=>p});var n=t(96559),a=t(48088),o=t(37719),i=t(32190),c=t(79464),u=t(45697);let l=u.Ik({subject:u.Yj().min(1).optional(),experience:u.ai().min(0).optional(),branch:u.Yj().min(1).optional(),photoUrl:u.Yj().optional(),tier:u.k5(["A_LEVEL","B_LEVEL","C_LEVEL","NEW"]).optional()});async function d(e,{params:r}){try{let{id:e}=await r,t=await c.z.teacher.findUnique({where:{id:e},include:{user:{select:{id:!0,name:!0,phone:!0,email:!0,role:!0,createdAt:!0}},groups:{include:{course:{select:{id:!0,name:!0,level:!0,duration:!0,price:!0}},enrollments:{include:{student:{include:{user:{select:{name:!0}}}}}},_count:{select:{enrollments:!0,classes:!0}}},orderBy:{createdAt:"desc"}},classes:{include:{group:{select:{id:!0,name:!0}},_count:{select:{attendances:!0}}},orderBy:{date:"desc"},take:20},_count:{select:{groups:!0,classes:!0}}}});if(!t)return i.NextResponse.json({error:"Teacher not found"},{status:404});let s=t.groups.reduce((e,r)=>e+r._count.enrollments,0);return i.NextResponse.json({...t,totalStudents:s})}catch(e){return console.error("Error fetching teacher:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,{params:r}){try{let{id:t}=await r,s=await e.json(),n=l.parse(s);if(!await c.z.teacher.findUnique({where:{id:t}}))return i.NextResponse.json({error:"Teacher not found"},{status:404});let a=await c.z.teacher.update({where:{id:t},data:{...n,updatedAt:new Date},include:{user:{select:{id:!0,name:!0,phone:!0,email:!0,role:!0}},_count:{select:{groups:!0,classes:!0}}}});return i.NextResponse.json(a)}catch(e){if(e instanceof u.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating teacher:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:r}){try{let{id:e}=await r,t=await c.z.teacher.findUnique({where:{id:e},include:{groups:!0}});if(!t)return i.NextResponse.json({error:"Teacher not found"},{status:404});if(t.groups.length>0){let e=t.groups.filter(e=>e.isActive);if(e.length>0)return i.NextResponse.json({error:"Cannot delete teacher with active groups",details:`Teacher has ${e.length} active group(s)`},{status:400})}return await c.z.teacher.delete({where:{id:e}}),i.NextResponse.json({message:"Teacher deleted successfully",deletedId:e})}catch(e){return console.error("Error deleting teacher:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/teachers/[id]/route",pathname:"/api/teachers/[id]",filename:"route",bundlePath:"app/api/teachers/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\teachers\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:f}=x;function w(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697],()=>t(26535));module.exports=s})();