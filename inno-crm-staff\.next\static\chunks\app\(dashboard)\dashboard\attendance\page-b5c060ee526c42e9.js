(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1893],{245:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var a=s(5155),r=s(2115),l=s(8482),n=s(7168),d=s(8145),c=s(9852),i=s(5784),o=s(8524),x=s(9840),u=s(9026),m=s(3999),h=s(2915),f=s(311),p=s(4186),j=s(8533),g=s(7624),y=s(4616),N=s(7924),v=s(7580),b=s(9074),w=s(4621),A=s(2525),k=s(2177),E=s(221),C=s(1153),S=s(2714),T=s(5670);let R=C.Ik({classId:C.Yj().min(1,"Class is required"),attendances:C.YO(C.Ik({studentId:C.Yj(),status:C.k5(["PRESENT","ABSENT","LATE","EXCUSED"]),notes:C.Yj().optional()})).min(1,"At least one student attendance is required")}),I=[{value:"PRESENT",label:"Present",icon:h.A,color:"text-green-600"},{value:"ABSENT",label:"Absent",icon:f.A,color:"text-red-600"},{value:"LATE",label:"Late",icon:p.A,color:"text-yellow-600"},{value:"EXCUSED",label:"Excused",icon:j.A,color:"text-blue-600"}],L=function(e){let{initialData:t,onSubmit:s,onCancel:o,isEditing:x=!1,preselectedClassId:m}=e,[j,y]=(0,r.useState)(!1),[N,w]=(0,r.useState)(null),[A,C]=(0,r.useState)([]),[L,F]=(0,r.useState)(null),[P,z]=(0,r.useState)([]),{handleSubmit:D,setValue:M,watch:Z,formState:{errors:U}}=(0,k.mN)({resolver:(0,E.u)(R),defaultValues:{classId:m||(null==t?void 0:t.classId)||"",attendances:(null==t?void 0:t.attendances)||[]}}),B=Z("classId");(0,r.useEffect)(()=>{q()},[]),(0,r.useEffect)(()=>{if(B){let e=A.find(e=>e.id===B);if(F(e||null),e){let t=e.group.enrollments.map(t=>{var s;let a=null==(s=e.attendances)?void 0:s.find(e=>e.studentId===t.student.id);return{studentId:t.student.id,studentName:t.student.user.name,studentPhone:t.student.user.phone,status:(null==a?void 0:a.status)||"PRESENT",notes:(null==a?void 0:a.notes)||""}});z(t),M("attendances",t.map(e=>({studentId:e.studentId,status:e.status,notes:e.notes})))}}},[B,A,M]);let q=async()=>{try{var e;let t=await fetch("/api/groups"),s=(null==(e=(await t.json()).groups)?void 0:e.map(e=>({id:e.id,date:new Date().toISOString(),topic:"".concat(e.course.name," - ").concat(e.course.level),group:{id:e.id,name:e.name,course:e.course,enrollments:e.enrollments||[]},teacher:e.teacher,attendances:[]})))||[];C(s)}catch(e){console.error("Error fetching groups for attendance:",e)}},W=async e=>{y(!0),w(null);try{await s(e)}catch(e){w(e instanceof Error?e.message:"An error occurred")}finally{y(!1)}},H=(e,t,s)=>{let a=P.map(a=>a.studentId===e?{...a,[t]:s}:a);z(a),M("attendances",a.map(e=>({studentId:e.studentId,status:e.status,notes:e.notes})))},V=e=>{let t=P.map(t=>({...t,status:e}));z(t),M("attendances",t.map(e=>({studentId:e.studentId,status:e.status,notes:e.notes})))},X=e=>{let t=I.find(t=>t.value===e);if(!t)return null;let s=t.icon;return(0,a.jsx)(s,{className:"h-4 w-4 ".concat(t.color)})},$=P.length,J=P.filter(e=>"PRESENT"===e.status).length,O=P.filter(e=>"ABSENT"===e.status).length,Y=P.filter(e=>"LATE"===e.status).length;P.filter(e=>"EXCUSED"===e.status).length;let _=$>0?(J+Y)/$*100:0;return(0,a.jsxs)(l.Zp,{className:"w-full max-w-4xl mx-auto",children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(T.A,{className:"h-5 w-5 mr-2"}),x?"Edit Attendance":"Mark Class Attendance"]}),(0,a.jsx)(l.BT,{children:x?"Update attendance records":"Mark attendance for students in the selected class"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("form",{onSubmit:D(W),className:"space-y-6",children:[N&&(0,a.jsx)(u.Fc,{variant:"destructive",children:(0,a.jsx)(u.TN,{children:N})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Class Information"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"classId",children:"Select Class *"}),(0,a.jsxs)(i.l6,{value:B,onValueChange:e=>M("classId",e),disabled:!!m,children:[(0,a.jsx)(i.bq,{className:U.classId?"border-red-500":"",children:(0,a.jsx)(i.yv,{placeholder:"Select class"})}),(0,a.jsx)(i.gC,{children:A.map(e=>(0,a.jsxs)(i.eb,{value:e.id,children:[e.group.name," - ",new Date(e.date).toLocaleDateString(),e.topic&&" - ".concat(e.topic)]},e.id))})]}),U.classId&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:U.classId.message}),L&&(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:L.group.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:L.group.course.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Teacher: ",L.teacher.user.name]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-medium",children:new Date(L.date).toLocaleDateString()}),(0,a.jsx)(d.E,{variant:"outline",children:L.group.course.level}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[L.group.enrollments.length," students"]})]})]}),L.topic&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t",children:(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Topic:"})," ",L.topic]})})]})]})]}),P.length>0&&(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-4 text-center",children:[(0,a.jsx)(v.A,{className:"h-6 w-6 mx-auto text-blue-600 mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:$}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total"})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-4 text-center",children:[(0,a.jsx)(h.A,{className:"h-6 w-6 mx-auto text-green-600 mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:J}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Present"})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-4 text-center",children:[(0,a.jsx)(f.A,{className:"h-6 w-6 mx-auto text-red-600 mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:O}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Absent"})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-4 text-center",children:[(0,a.jsx)(p.A,{className:"h-6 w-6 mx-auto text-yellow-600 mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:Y}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Late"})]})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4 text-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[_.toFixed(1),"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Rate"})]})})})]}),P.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Quick Actions"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>V("PRESENT"),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2 text-green-600"}),"Mark All Present"]}),(0,a.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>V("ABSENT"),children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2 text-red-600"}),"Mark All Absent"]}),(0,a.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>V("LATE"),children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2 text-yellow-600"}),"Mark All Late"]})]})]}),P.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Student Attendance"})]}),(0,a.jsx)("div",{className:"space-y-3",children:P.map(e=>(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.studentName}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.studentPhone})]}),(0,a.jsx)("div",{className:"flex space-x-2",children:I.map(t=>(0,a.jsxs)(n.$,{type:"button",variant:e.status===t.value?"default":"outline",size:"sm",onClick:()=>H(e.studentId,"status",t.value),className:e.status===t.value?"":"hover:bg-gray-50",children:[X(t.value),(0,a.jsx)("span",{className:"ml-1 hidden sm:inline",children:t.label})]},t.value))}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(c.p,{placeholder:"Notes (optional)",value:e.notes,onChange:t=>H(e.studentId,"notes",t.target.value),className:"text-sm"})})]})})},e.studentId))})]}),U.attendances&&(0,a.jsx)(u.Fc,{variant:"destructive",children:(0,a.jsx)(u.TN,{children:U.attendances.message})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[o&&(0,a.jsx)(n.$,{type:"button",variant:"outline",onClick:o,children:"Cancel"}),(0,a.jsxs)(n.$,{type:"submit",disabled:j||0===P.length,children:[j&&(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),x?"Update Attendance":"Save Attendance"]})]})]})})]})};var F=s(2108);function P(){var e,t;let{data:s}=(0,F.useSession)(),[k,E]=(0,r.useState)([]),[C,S]=(0,r.useState)(!0),[T,R]=(0,r.useState)(""),[I,P]=(0,r.useState)("ALL"),[z,D]=(0,r.useState)(""),[M,Z]=(0,r.useState)(!1),[U,B]=(0,r.useState)(!1),[q,W]=(0,r.useState)(null),[H,V]=(0,r.useState)(!1),[X,$]=(0,r.useState)(null),J=(0,r.useCallback)(async()=>{try{var e,t;S(!0);let a="/api/attendance?limit=50";"ALL"!==I&&(a+="&status=".concat(I)),z&&(a+="&dateFrom=".concat(z,"&dateTo=").concat(z)),(null==s||null==(e=s.user)?void 0:e.role)==="TEACHER"&&(null==s||null==(t=s.user)?void 0:t.id)&&(a+="&teacherId=".concat(s.user.id));let r=await fetch(a),l=await r.json();E(l.attendances||[]),$(null)}catch(e){console.error("Error fetching attendances:",e),$("Failed to fetch attendance records")}finally{S(!1)}},[I,z,null==s||null==(e=s.user)?void 0:e.role,null==s||null==(t=s.user)?void 0:t.id]),O=async e=>{V(!0),$(null);try{let t=await fetch("/api/attendance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to create attendance")}Z(!1),J()}catch(e){$(e instanceof Error?e.message:"An error occurred")}finally{V(!1)}},Y=async e=>{if(q){V(!0),$(null);try{let t=await fetch("/api/attendance/".concat(q.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to update attendance")}B(!1),W(null),J()}catch(e){$(e instanceof Error?e.message:"An error occurred")}finally{V(!1)}}},_=async e=>{if(confirm("Are you sure you want to delete this attendance record?"))try{let t=await fetch("/api/attendance/".concat(e),{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to delete attendance")}J()}catch(e){$(e instanceof Error?e.message:"An error occurred")}};(0,r.useEffect)(()=>{J()},[J]);let G=k.filter(e=>e.student.user.name.toLowerCase().includes(T.toLowerCase())||e.student.user.phone.includes(T)||e.class.group.name.toLowerCase().includes(T.toLowerCase())||e.class.teacher.user.name.toLowerCase().includes(T.toLowerCase())),Q=e=>{switch(e){case"PRESENT":return(0,a.jsx)(h.A,{className:"h-4 w-4 text-green-600"});case"ABSENT":return(0,a.jsx)(f.A,{className:"h-4 w-4 text-red-600"});case"LATE":return(0,a.jsx)(p.A,{className:"h-4 w-4 text-yellow-600"});case"EXCUSED":return(0,a.jsx)(j.A,{className:"h-4 w-4 text-blue-600"});default:return null}},K=e=>{switch(e){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"EXCUSED":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},ee=k.length,et=k.filter(e=>"PRESENT"===e.status).length,es=k.filter(e=>"ABSENT"===e.status).length,ea=k.filter(e=>"LATE"===e.status).length;k.filter(e=>"EXCUSED"===e.status).length;let er=ee>0?(et+ea)/ee*100:0;return C?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(g.A,{className:"h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading attendance records..."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[X&&(0,a.jsx)(u.Fc,{variant:"destructive",children:(0,a.jsx)(u.TN,{children:X})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Attendance Tracking"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Monitor and manage student attendance"})]}),(0,a.jsxs)(x.lG,{open:M,onOpenChange:Z,children:[(0,a.jsx)(x.zM,{asChild:!0,children:(0,a.jsxs)(n.$,{children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Mark Attendance"]})}),(0,a.jsxs)(x.Cf,{className:"max-w-5xl",children:[(0,a.jsxs)(x.c7,{children:[(0,a.jsx)(x.L3,{children:"Mark Class Attendance"}),(0,a.jsx)(x.rr,{children:"Select a class and mark attendance for all students."})]}),(0,a.jsx)(L,{onSubmit:O,onCancel:()=>Z(!1),isEditing:!1})]})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Search & Filter"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(N.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{placeholder:"Search by student, group, or teacher...",value:T,onChange:e=>R(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(i.l6,{value:I,onValueChange:P,children:[(0,a.jsx)(i.bq,{children:(0,a.jsx)(i.yv,{placeholder:"Filter by status"})}),(0,a.jsxs)(i.gC,{children:[(0,a.jsx)(i.eb,{value:"ALL",children:"All Status"}),(0,a.jsx)(i.eb,{value:"PRESENT",children:"Present"}),(0,a.jsx)(i.eb,{value:"ABSENT",children:"Absent"}),(0,a.jsx)(i.eb,{value:"LATE",children:"Late"}),(0,a.jsx)(i.eb,{value:"EXCUSED",children:"Excused"})]})]}),(0,a.jsx)(c.p,{type:"date",value:z,onChange:e=>D(e.target.value),placeholder:"Filter by date"})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Records"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ee})]})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Present"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:et})]})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 text-red-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Absent"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:es})]})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-yellow-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Late"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:ea})]})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-purple-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Attendance Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[er.toFixed(1),"%"]})]})]})})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{children:["Attendance Records (",G.length,")"]}),(0,a.jsx)(l.BT,{children:"Recent attendance records across all classes"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)(o.XI,{children:[(0,a.jsx)(o.A0,{children:(0,a.jsxs)(o.Hj,{children:[(0,a.jsx)(o.nd,{children:"Student"}),(0,a.jsx)(o.nd,{children:"Group"}),(0,a.jsx)(o.nd,{children:"Teacher"}),(0,a.jsx)(o.nd,{children:"Class Date"}),(0,a.jsx)(o.nd,{children:"Topic"}),(0,a.jsx)(o.nd,{children:"Status"}),(0,a.jsx)(o.nd,{children:"Notes"}),(0,a.jsx)(o.nd,{children:"Actions"})]})}),(0,a.jsx)(o.BF,{children:G.map(e=>(0,a.jsxs)(o.Hj,{children:[(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.student.user.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.student.user.phone})]})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)("span",{className:"font-medium",children:e.class.group.name})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)("span",{className:"text-sm",children:e.class.teacher.user.name})}),(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:(0,m.Yq)(e.class.date)})]})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e.class.topic||"No topic"})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)(d.E,{className:K(e.status),children:(0,a.jsxs)("div",{className:"flex items-center",children:[Q(e.status),(0,a.jsx)("span",{className:"ml-1",children:e.status})]})})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)("span",{className:"text-sm text-gray-600",children:e.notes||"-"})}),(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{W(e),B(!0)},children:(0,a.jsx)(w.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>_(e.id),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(A.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})]}),(0,a.jsx)(x.lG,{open:U,onOpenChange:B,children:(0,a.jsxs)(x.Cf,{className:"max-w-5xl",children:[(0,a.jsxs)(x.c7,{children:[(0,a.jsx)(x.L3,{children:"Edit Attendance"}),(0,a.jsx)(x.rr,{children:"Update attendance record for this student."})]}),q&&(0,a.jsx)(L,{preselectedClassId:q.classId,initialData:{classId:q.classId,attendances:[{studentId:q.studentId,status:q.status,notes:q.notes||""}]},onSubmit:Y,onCancel:()=>{B(!1),W(null)},isEditing:!0})]})})]})}},311:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},2525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2620:(e,t,s)=>{Promise.resolve().then(s.bind(s,245))},2714:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var a=s(5155),r=s(2115),l=s(968),n=s(2085),d=s(3999);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,d.cn)(c(),s),...r})});i.displayName=l.b.displayName},2915:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3999:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>d,cn:()=>l,r6:()=>c,vv:()=>n});var a=s(2596),r=s(9688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function n(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function d(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4621:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},5670:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},5784:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>p,gC:()=>f,l6:()=>o,yv:()=>x});var a=s(5155),r=s(2115),l=s(1992),n=s(6474),d=s(7863),c=s(5196),i=s(3999);let o=l.bL;l.YJ;let x=l.WT,u=r.forwardRef((e,t)=>{let{className:s,children:r,...d}=e;return(0,a.jsxs)(l.l9,{ref:t,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...d,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.PP,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});m.displayName=l.PP.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wn,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let f=r.forwardRef((e,t)=>{let{className:s,children:r,position:n="popper",...d}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:t,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...d,children:[(0,a.jsx)(m,{}),(0,a.jsx)(l.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});f.displayName=l.UC.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.JU,{ref:t,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=l.JU.displayName;let p=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(l.q7,{ref:t,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});p.displayName=l.q7.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=l.wv.displayName},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>i,r:()=>c});var a=s(5155),r=s(2115),l=s(9708),n=s(2085),d=s(3999);let c=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=r.forwardRef((e,t)=>{let{className:s,variant:r,size:n,asChild:i=!1,...o}=e,x=i?l.DX:"button";return(0,a.jsx)(x,{className:(0,d.cn)(c({variant:r,size:n,className:s})),ref:t,...o})});i.displayName="Button"},7580:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7624:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8145:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(5155);s(2115);var r=s(2085),l=s(3999);let n=(0,r.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:s}),t),...r})}},8482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>i,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>d});var a=s(5155),r=s(2115),l=s(3999);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",s),...r})});n.displayName="Card";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});d.displayName="CardHeader";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});c.displayName="CardTitle";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});i.displayName="CardDescription";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});o.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"},8524:(e,t,s)=>{"use strict";s.d(t,{A0:()=>d,BF:()=>c,Hj:()=>i,XI:()=>n,nA:()=>x,nd:()=>o});var a=s(5155),r=s(2115),l=s(3999);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",s),...r})})});n.displayName="Table";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",s),...r})});d.displayName="TableHeader";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...r})});c.displayName="TableBody",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});i.displayName="TableRow";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});o.displayName="TableHead";let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});x.displayName="TableCell",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),...r})}).displayName="TableCaption"},8533:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},9026:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>c,TN:()=>i});var a=s(5155),r=s(2115),l=s(2085),n=s(3999);let d=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,...l}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(d({variant:r}),s),...l})});c.displayName="Alert",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",s),...r})}).displayName="AlertTitle";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",s),...r})});i.displayName="AlertDescription"},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9840:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,L3:()=>h,c7:()=>m,lG:()=>c,rr:()=>f,zM:()=>i});var a=s(5155),r=s(2115),l=s(5452),n=s(4416),d=s(3999);let c=l.bL,i=l.l9,o=l.ZL;l.bm;let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.hJ,{ref:t,className:(0,d.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});x.displayName=l.hJ.displayName;let u=r.forwardRef((e,t)=>{let{className:s,children:r,...c}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.UC,{ref:t,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",s),...c,children:(0,a.jsxs)("div",{className:"relative",children:[r,(0,a.jsxs)(l.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]})});u.displayName=l.UC.displayName;let m=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};m.displayName="DialogHeader";let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.hE,{ref:t,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});h.displayName=l.hE.displayName;let f=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.VY,{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",s),...r})});f.displayName=l.VY.displayName},9852:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(5155),r=s(2115),l=s(3999);let n=r.forwardRef((e,t)=>{let{className:s,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});n.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,6221,4358,1071,2356,2108,8441,1684,7358],()=>t(2620)),_N_E=e.O()}]);