(()=>{var e={};e.id=3676,e.ids=[3676],e.modules={439:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18618).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1215:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18618).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40918:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18618).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},51358:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>d});var r=t(37413),a=t(61120),n=t(66819);let i=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));i.displayName="Card";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},59379:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(37413),a=t(51358),n=t(99455),i=t(94592),d=t(53148);let l=(0,t(18618).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);var c=t(439),o=t(40918),x=t(89217),m=t(88804),u=t(1215),h=t(65516),p=t(75234);function g(){let e=[{id:1,title:"Essay: My Dream Vacation",course:"General English B1",dueDate:"2024-01-20",description:"Write a 300-word essay about your dream vacation destination",type:"Essay",maxScore:100,status:"pending",priority:"high"},{id:2,title:"Vocabulary Quiz: Unit 6",course:"General English B1",dueDate:"2024-01-24",description:"Complete the vocabulary quiz covering travel and tourism terms",type:"Quiz",maxScore:50,status:"pending",priority:"medium"}],s=[{id:3,title:"Grammar Exercise: Present Perfect",course:"General English B1",submittedDate:"2024-01-12",dueDate:"2024-01-15",score:85,maxScore:100,feedback:"Good understanding of present perfect tense. Work on irregular verbs.",status:"graded",type:"Exercise"},{id:4,title:"Speaking Recording: Describing Places",course:"General English B1",submittedDate:"2024-01-10",dueDate:"2024-01-12",score:78,maxScore:100,feedback:"Clear pronunciation. Try to use more descriptive adjectives.",status:"graded",type:"Speaking"}],t=[{id:5,title:"Reading Comprehension Test",course:"General English B1",assignDate:"2024-01-25",dueDate:"2024-01-30",description:"Test on reading comprehension skills",type:"Test",maxScore:100,status:"upcoming"}],g=e=>{switch(e){case"pending":return"bg-orange-100 text-orange-800";case"submitted":return"bg-blue-100 text-blue-800";case"graded":return"bg-green-100 text-green-800";case"upcoming":default:return"bg-gray-100 text-gray-800";case"overdue":return"bg-red-100 text-red-800"}},f=e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{switch(e){case"pending":return(0,r.jsx)(d.A,{className:"h-4 w-4"});case"submitted":return(0,r.jsx)(l,{className:"h-4 w-4"});case"graded":return(0,r.jsx)(c.A,{className:"h-4 w-4"});case"upcoming":return(0,r.jsx)(o.A,{className:"h-4 w-4"});case"overdue":return(0,r.jsx)(x.A,{className:"h-4 w-4"});default:return(0,r.jsx)(m.A,{className:"h-4 w-4"})}},j=(e,s)=>{let t=e/s*100;return t>=80?"text-green-600":t>=60?"text-yellow-600":"text-red-600"};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Assignments"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track and submit your course assignments"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:"Pending"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-orange-600"})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.length}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Due soon"})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:"Submitted"}),(0,r.jsx)(l,{className:"h-4 w-4 text-blue-600"})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:s.length}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Awaiting grades"})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:"Graded"}),(0,r.jsx)(c.A,{className:"h-4 w-4 text-green-600"})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:s.filter(e=>"graded"===e.status).length}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Completed"})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:"Upcoming"}),(0,r.jsx)(o.A,{className:"h-4 w-4 text-gray-600"})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t.length}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Future assignments"})]})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsxs)(a.ZB,{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2 text-orange-600"}),"Pending Assignments"]}),(0,r.jsx)(a.BT,{children:"Assignments that need to be completed"})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-orange-50",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-orange-100 rounded-full",children:y(e.status)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.course}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 text-gray-400"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Due: ",e.dueDate]})]}),(0,r.jsx)(n.E,{variant:"outline",className:"text-xs",children:e.type}),(0,r.jsxs)(n.E,{className:f(e.priority),children:[e.priority," priority"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm font-medium",children:[e.maxScore," pts"]}),(0,r.jsxs)(i.$,{size:"sm",children:[(0,r.jsx)(l,{className:"h-4 w-4 mr-1"}),"Submit"]})]})]},e.id))})})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsxs)(a.ZB,{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Submitted Assignments"]}),(0,r.jsx)(a.BT,{children:"Your completed assignments and grades"})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-full",children:y(e.status)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.course}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 text-gray-400"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Submitted: ",e.submittedDate]})]}),(0,r.jsx)(n.E,{variant:"outline",className:"text-xs",children:e.type})]}),e.feedback&&(0,r.jsxs)("div",{className:"mt-2 p-2 bg-gray-50 rounded text-sm",children:[(0,r.jsx)("strong",{children:"Feedback:"})," ",e.feedback]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:`text-lg font-bold ${j(e.score,e.maxScore)}`,children:[e.score,"/",e.maxScore]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[Math.round(e.score/e.maxScore*100),"%"]})]}),(0,r.jsx)(n.E,{className:g(e.status),children:e.status}),(0,r.jsxs)(i.$,{size:"sm",variant:"outline",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"View"]})]})]},e.id))})})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsxs)(a.ZB,{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Upcoming Assignments"]}),(0,r.jsx)(a.BT,{children:"Future assignments to be released"})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full",children:y(e.status)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.course}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 text-gray-400"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Available: ",e.assignDate]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"h-3 w-3 text-gray-400"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Due: ",e.dueDate]})]}),(0,r.jsx)(n.E,{variant:"outline",className:"text-xs",children:e.type})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm font-medium",children:[e.maxScore," pts"]}),(0,r.jsx)(n.E,{className:g(e.status),children:e.status})]})]},e.id))})})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsx)(a.ZB,{children:"Quick Actions"})}),(0,r.jsx)(a.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)(i.$,{variant:"outline",className:"h-16 flex flex-col space-y-1",children:[(0,r.jsx)(l,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"text-sm",children:"Submit Assignment"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-16 flex flex-col space-y-1",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"text-sm",children:"Download Materials"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-16 flex flex-col space-y-1",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"text-sm",children:"Study Resources"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-16 flex flex-col space-y-1",children:[(0,r.jsx)(o.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"text-sm",children:"Assignment Calendar"})]})]})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65516:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18618).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},66819:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var r=t(75986),a=t(8974);function n(...e){return(0,a.QP)((0,r.$)(e))}},75234:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18618).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},78335:()=>{},82431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),d=t(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(s,l);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["student",{children:["assignments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59379)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\assignments\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\assignments\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/student/assignments/page",pathname:"/dashboard/student/assignments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88804:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18618).A)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},89217:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18618).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94592:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var r=t(37413),a=t(61120),n=t(70403),i=t(50662),d=t(66819);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...i},c)=>{let o=a?n.DX:"button";return(0,r.jsx)(o,{className:(0,d.cn)(l({variant:s,size:t,className:e})),ref:c,...i})});c.displayName="Button"},96487:()=>{},99455:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var r=t(37413);t(61120);var a=t(50662),n=t(66819);let i=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:s,...t}){return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:s}),e),...t})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,7615,2918,8887,7144,6631],()=>t(82431));module.exports=r})();