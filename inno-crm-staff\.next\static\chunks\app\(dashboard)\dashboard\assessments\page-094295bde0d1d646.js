(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4541],{311:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},968:(e,s,t)=>{"use strict";t.d(s,{b:()=>d});var a=t(2115),r=t(3655),l=t(5155),n=a.forwardRef((e,s)=>(0,l.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var d=n},1618:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(5155),r=t(2115),l=t(2108),n=t(8482),d=t(7168),i=t(9852),c=t(8145),o=t(5784),m=t(9840),u=t(8524),x=t(2714),h=t(3999),p=t(2915),f=t(311),j=t(4616),g=t(7580),y=t(7434),v=t(7949),N=t(9037),b=t(7924),w=t(3580);function A(){var e;let{data:s}=(0,l.useSession)(),[t,A]=(0,r.useState)([]),[S,T]=(0,r.useState)([]),[R,E]=(0,r.useState)(!0),[k,C]=(0,r.useState)(""),[F,L]=(0,r.useState)("all"),[_,M]=(0,r.useState)(!1),[I,O]=(0,r.useState)(null),[P,D]=(0,r.useState)(""),[z,U]=(0,r.useState)(""),[G,V]=(0,r.useState)({}),{toast:q}=(0,w.dj)(),Z=["ADMIN","MANAGER","TEACHER"].includes(null==s||null==(e=s.user)?void 0:e.role),B=(0,r.useCallback)(async()=>{E(!0);try{let e=new URLSearchParams;F&&"all"!==F&&e.append("type",F);let s=await fetch("/api/assessments?".concat(e));if(!s.ok)throw Error("Failed to fetch assessments");let t=await s.json();A(t.assessments||[])}catch(e){q({variant:"destructive",title:"Error",description:"Failed to fetch assessments"})}finally{E(!1)}},[F,q]),J=async()=>{try{let e=await fetch("/api/groups");if(!e.ok)throw Error("Failed to fetch groups");let s=await e.json();T(s.groups||[])}catch(e){console.error("Error fetching groups:",e),T([])}};(0,r.useEffect)(()=>{Z&&(B(),J())},[B,Z]);let H=t.filter(e=>{var s,t;let a=(null==(s=e.student)?void 0:s.user.name)||"",r=(null==(t=e.group)?void 0:t.name)||"",l=e.testName||"";return a.toLowerCase().includes(k.toLowerCase())||r.toLowerCase().includes(k.toLowerCase())||l.toLowerCase().includes(k.toLowerCase())||e.type.toLowerCase().includes(k.toLowerCase())}),W=async e=>{if(e.preventDefault(),!I||!P||!z)return void q({variant:"destructive",title:"Error",description:"Please select a group, enter test name, and select test type"});try{let e=I.enrollments.map(e=>{let s=e.student.id,t=G[s];return t?fetch("/api/assessments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:s,groupId:I.id,testName:P,type:z,level:I.course.level,score:t.score,maxScore:t.maxScore,passed:t.passed,completedAt:new Date().toISOString()})}):null}).filter(Boolean);await Promise.all(e),q({title:"Success",description:"Assessment results recorded for ".concat(e.length," students")}),M(!1),X(),B()}catch(e){q({variant:"destructive",title:"Error",description:"Failed to record assessment results"})}},X=()=>{O(null),D(""),U(""),V({})},$=e=>{switch(e){case"LEVEL_TEST":return"bg-green-100 text-green-800";case"PROGRESS_TEST":return"bg-yellow-100 text-yellow-800";case"FINAL_EXAM":return"bg-red-100 text-red-800";case"GROUP_TEST":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},Y=e=>e?(0,a.jsx)(p.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),Q=(e,s,t)=>{V(a=>({...a,[e]:{...a[e],[s]:t}}))};return Z?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Assessment Recording"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Record post-test results for student groups"})]}),(0,a.jsxs)(m.lG,{open:_,onOpenChange:M,children:[(0,a.jsx)(m.zM,{asChild:!0,children:(0,a.jsxs)(d.$,{onClick:X,children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Record Test Results"]})}),(0,a.jsxs)(m.Cf,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{children:"Record Test Results"}),(0,a.jsx)(m.rr,{children:"Select a group and enter test results for all students."})]}),(0,a.jsxs)("form",{onSubmit:W,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Step 1: Select Student Group"})]}),(0,a.jsxs)(o.l6,{value:(null==I?void 0:I.id)||"",onValueChange:e=>{O(S.find(s=>s.id===e)||null),V({})},children:[(0,a.jsx)(o.bq,{children:(0,a.jsx)(o.yv,{placeholder:"Select a student group"})}),(0,a.jsx)(o.gC,{children:S.map(e=>(0,a.jsxs)(o.eb,{value:e.id,children:[e.name," - ",e.course.name," (",e.course.level,")"]},e.id))})]})]}),I&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Step 2: Test Information"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:"testName",children:"Test Name *"}),(0,a.jsx)(i.p,{id:"testName",value:P,onChange:e=>D(e.target.value),placeholder:"e.g., Unit 5 Test, Mid-term Exam",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:"testType",children:"Test Type *"}),(0,a.jsxs)(o.l6,{value:z,onValueChange:U,children:[(0,a.jsx)(o.bq,{children:(0,a.jsx)(o.yv,{placeholder:"Select test type"})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"LEVEL_TEST",children:"Level Test"}),(0,a.jsx)(o.eb,{value:"PROGRESS_TEST",children:"Progress Test"}),(0,a.jsx)(o.eb,{value:"FINAL_EXAM",children:"Final Exam"}),(0,a.jsx)(o.eb,{value:"GROUP_TEST",children:"Group Test"})]})]})]})]})]}),I&&P&&z&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Step 3: Enter Individual Scores"})]}),(0,a.jsx)("div",{className:"border rounded-lg p-4 max-h-60 overflow-y-auto",children:(0,a.jsx)("div",{className:"space-y-3",children:I.enrollments.map(e=>{let s=e.student.id,t=G[s]||{score:0,maxScore:100,passed:!1};return(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-3 items-center p-3 border rounded bg-gray-50",children:[(0,a.jsx)("div",{className:"col-span-2",children:(0,a.jsx)("span",{className:"font-medium",children:e.student.user.name})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:"score-".concat(s),className:"text-xs",children:"Score"}),(0,a.jsx)(i.p,{id:"score-".concat(s),type:"number",min:"0",value:t.score,onChange:e=>{let a=parseInt(e.target.value)||0,r=a>=.6*t.maxScore;Q(s,"score",a),Q(s,"passed",r)},className:"h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:"maxScore-".concat(s),className:"text-xs",children:"Max Score"}),(0,a.jsx)(i.p,{id:"maxScore-".concat(s),type:"number",min:"1",value:t.maxScore,onChange:e=>{let a=parseInt(e.target.value)||100,r=t.score>=.6*a;Q(s,"maxScore",a),Q(s,"passed",r)},className:"h-8"})]}),(0,a.jsx)("div",{className:"flex items-center justify-center",children:t.passed?(0,a.jsx)(p.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(f.A,{className:"h-5 w-5 text-red-600"})})]},s)})})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[(0,a.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>M(!1),children:"Cancel"}),(0,a.jsx)(d.$,{type:"submit",disabled:!I||!P||!z||0===Object.keys(G).length,children:"Record Test Results"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Assessments"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Passed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.filter(e=>e.passed).length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 text-red-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Failed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.filter(e=>!e.passed).length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"h-8 w-8 text-purple-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pass Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[t.length>0?Math.round(t.filter(e=>e.passed).length/t.length*100):0,"%"]})]})]})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Filters"}),(0,a.jsx)(n.BT,{children:"Filter assessments by type and search"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(i.p,{placeholder:"Search students, types...",value:k,onChange:e=>C(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(o.l6,{value:F,onValueChange:L,children:[(0,a.jsx)(o.bq,{children:(0,a.jsx)(o.yv,{placeholder:"All Types"})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"all",children:"All Types"}),(0,a.jsx)(o.eb,{value:"LEVEL_TEST",children:"Level Test"}),(0,a.jsx)(o.eb,{value:"PROGRESS_TEST",children:"Progress Test"}),(0,a.jsx)(o.eb,{value:"FINAL_EXAM",children:"Final Exam"}),(0,a.jsx)(o.eb,{value:"GROUP_TEST",children:"Group Test"})]})]}),(0,a.jsxs)(d.$,{onClick:B,variant:"outline",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Search"]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Assessment Records"}),(0,a.jsxs)(n.BT,{children:["Showing ",H.length," recorded test results"]})]}),(0,a.jsxs)(n.Wu,{children:[R?(0,a.jsx)("div",{className:"flex justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)(u.XI,{children:[(0,a.jsx)(u.A0,{children:(0,a.jsxs)(u.Hj,{children:[(0,a.jsx)(u.nd,{children:"Student/Group"}),(0,a.jsx)(u.nd,{children:"Test Name"}),(0,a.jsx)(u.nd,{children:"Type"}),(0,a.jsx)(u.nd,{children:"Level"}),(0,a.jsx)(u.nd,{children:"Score"}),(0,a.jsx)(u.nd,{children:"Result"}),(0,a.jsx)(u.nd,{children:"Completed"})]})}),(0,a.jsx)(u.BF,{children:H.map(e=>(0,a.jsxs)(u.Hj,{children:[(0,a.jsx)(u.nA,{children:(0,a.jsx)("div",{children:e.student?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"font-medium",children:e.student.user.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.student.user.email})]}):e.group?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"font-medium",children:e.group.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.group.course.name})]}):(0,a.jsx)("span",{className:"text-gray-400",children:"-"})})}),(0,a.jsx)(u.nA,{children:(0,a.jsx)("div",{className:"font-medium",children:e.testName||"Legacy Assessment"})}),(0,a.jsx)(u.nA,{children:(0,a.jsx)(c.E,{className:$(e.type),children:e.type.replace("_"," ")})}),(0,a.jsx)(u.nA,{children:e.level||"-"}),(0,a.jsx)(u.nA,{children:void 0!==e.score&&e.maxScore?"".concat(e.score,"/").concat(e.maxScore):"-"}),(0,a.jsx)(u.nA,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[Y(e.passed),(0,a.jsx)("span",{children:e.passed?"Passed":"Failed"})]})}),(0,a.jsx)(u.nA,{children:e.completedAt?(0,h.Yq)(e.completedAt):"-"})]},e.id))})]})}),0===H.length&&!R&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(y.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No assessment records found."}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Start by recording test results for a student group."})]})]})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(f.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Only Test Administrators can access the Assessment system."})]})})}},2714:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var a=t(5155),r=t(2115),l=t(968),n=t(2085),d=t(3999);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.b,{ref:s,className:(0,d.cn)(i(),t),...r})});c.displayName=l.b.displayName},2915:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3580:(e,s,t)=>{"use strict";t.d(s,{dj:()=>u});var a=t(2115);let r=0,l=new Map,n=e=>{if(l.has(e))return;let s=setTimeout(()=>{l.delete(e),o({type:"REMOVE_TOAST",toastId:e})},5e3);l.set(e,s)},d=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=s;return t?n(t):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},i=[],c={toasts:[]};function o(e){c=d(c,e),i.forEach(e=>{e(c)})}function m(e){let{...s}=e,t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>o({type:"DISMISS_TOAST",toastId:t});return o({type:"ADD_TOAST",toast:{...s,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,s]=a.useState(c);return a.useEffect(()=>(i.push(s),()=>{let e=i.indexOf(s);e>-1&&i.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}},3999:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>d,cn:()=>l,r6:()=>i,vv:()=>n});var a=t(2596),r=t(9688);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}function n(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function d(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(s)}function i(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(s)}},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5784:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>o,yv:()=>m});var a=t(5155),r=t(2115),l=t(1992),n=t(6474),d=t(7863),i=t(5196),c=t(3999);let o=l.bL;l.YJ;let m=l.WT,u=r.forwardRef((e,s)=>{let{className:t,children:r,...d}=e;return(0,a.jsxs)(l.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...d,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,position:n="popper",...d}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...d,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let f=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(l.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});f.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.wv.displayName},7168:(e,s,t)=>{"use strict";t.d(s,{$:()=>c,r:()=>i});var a=t(5155),r=t(2115),l=t(9708),n=t(2085),d=t(3999);let i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:c=!1,...o}=e,m=c?l.DX:"button";return(0,a.jsx)(m,{className:(0,d.cn)(i({variant:r,size:n,className:t})),ref:s,...o})});c.displayName="Button"},7204:(e,s,t)=>{Promise.resolve().then(t.bind(t,1618))},7434:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7949:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]])},8145:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(5155);t(2115);var r=t(2085),l=t(3999);let n=(0,r.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),s),...r})}},8482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>d});var a=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",t),...r})});n.displayName="Card";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});d.displayName="CardHeader";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});i.displayName="CardTitle";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});c.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},8524:(e,s,t)=>{"use strict";t.d(s,{A0:()=>d,BF:()=>i,Hj:()=>c,XI:()=>n,nA:()=>m,nd:()=>o});var a=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:s,className:(0,l.cn)("w-full caption-bottom text-sm",t),...r})})});n.displayName="Table";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("thead",{ref:s,className:(0,l.cn)("[&_tr]:border-b",t),...r})});d.displayName="TableHeader";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tbody",{ref:s,className:(0,l.cn)("[&_tr:last-child]:border-0",t),...r})});i.displayName="TableBody",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tfoot",{ref:s,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...r})}).displayName="TableFooter";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tr",{ref:s,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...r})});c.displayName="TableRow";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("th",{ref:s,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...r})});o.displayName="TableHead";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("td",{ref:s,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...r})});m.displayName="TableCell",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("caption",{ref:s,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",t),...r})}).displayName="TableCaption"},9037:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},9840:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>u,L3:()=>h,c7:()=>x,lG:()=>i,rr:()=>p,zM:()=>c});var a=t(5155),r=t(2115),l=t(5452),n=t(4416),d=t(3999);let i=l.bL,c=l.l9,o=l.ZL;l.bm;let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.hJ,{ref:s,className:(0,d.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});m.displayName=l.hJ.displayName;let u=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(m,{}),(0,a.jsx)(l.UC,{ref:s,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",t),...i,children:(0,a.jsxs)("div",{className:"relative",children:[r,(0,a.jsxs)(l.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]})});u.displayName=l.UC.displayName;let x=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};x.displayName="DialogHeader";let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.hE,{ref:s,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});h.displayName=l.hE.displayName;let p=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.VY,{ref:s,className:(0,d.cn)("text-sm text-muted-foreground",t),...r})});p.displayName=l.VY.displayName},9852:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,s)=>{let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});n.displayName="Input"}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,1071,2108,8441,1684,7358],()=>s(7204)),_N_E=e.O()}]);