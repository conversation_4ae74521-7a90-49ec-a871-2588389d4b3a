(()=>{var e={};e.id=7163,e.ids=[7163],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35848:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>h,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>d});var n=t(96559),a=t(48088),o=t(37719),i=t(32190),c=t(79464),u=t(45697);let p=u.Ik({userId:u.Yj(),subject:u.Yj().min(1),experience:u.ai().min(0).optional(),branch:u.Yj().min(1),photoUrl:u.Yj().optional(),tier:u.k5(["A_LEVEL","B_LEVEL","C_LEVEL","NEW"]).default("NEW")});async function l(e){try{let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),n=r.get("search"),a=r.get("subject"),o=r.get("branch"),u={};n&&(u.OR=[{user:{name:{contains:n,mode:"insensitive"}}},{user:{phone:{contains:n}}},{user:{email:{contains:n,mode:"insensitive"}}},{subject:{contains:n,mode:"insensitive"}}]),a&&(u.subject={contains:a,mode:"insensitive"}),o&&(u.branch="main"===o?"Main Branch":"Branch");let[p,l]=await Promise.all([c.z.teacher.findMany({where:u,include:{user:{select:{id:!0,name:!0,phone:!0,email:!0,role:!0,createdAt:!0}},groups:{include:{course:{select:{name:!0,level:!0}},_count:{select:{enrollments:!0}}}},classes:{include:{group:{select:{name:!0}}},orderBy:{date:"desc"},take:5},_count:{select:{groups:!0,classes:!0}}},orderBy:{createdAt:"desc"},skip:(t-1)*s,take:s}),c.z.teacher.count({where:u})]);return i.NextResponse.json({teachers:p,pagination:{page:t,limit:s,total:l,pages:Math.ceil(l/s)}})}catch(e){return console.error("Error fetching teachers:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let r=await e.json(),t=p.parse(r),s=await c.z.user.findUnique({where:{id:t.userId}});if(!s)return i.NextResponse.json({error:"User not found"},{status:400});if("TEACHER"!==s.role)return i.NextResponse.json({error:"User must have TEACHER role"},{status:400});if(await c.z.teacher.findUnique({where:{userId:t.userId}}))return i.NextResponse.json({error:"Teacher profile already exists for this user"},{status:400});let n=await c.z.teacher.create({data:t,include:{user:{select:{id:!0,name:!0,phone:!0,email:!0,role:!0}},_count:{select:{groups:!0,classes:!0}}}});return i.NextResponse.json(n,{status:201})}catch(e){if(e instanceof u.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating teacher:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/teachers/route",pathname:"/api/teachers",filename:"route",bundlePath:"app/api/teachers/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\teachers\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:g}=h;function j(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697],()=>t(35848));module.exports=s})();