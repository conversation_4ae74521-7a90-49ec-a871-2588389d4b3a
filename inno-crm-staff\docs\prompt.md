# Innovative Centre CRM System - Next.js Development Prompt

## PROJECT OVERVIEW
Build a modern, comprehensive Customer Relationship Management (CRM) system for Innovative Centre using **Next.js 14** with **App Router**, optimized for **Vercel deployment** and ready to run with `npm run dev`.

## BUSINESS CONTEXT
- **Institution**: Innovative Centre - English language center in Uzbekistan
- **Student Base**: 4,000+ students across 2 branches
- **Course Types**: General English, IELTS, SAT, Math (primarily English focus)
- **Age Groups**: Kids to adults
- **Class Formats**: Group (12-30 students), Individual, Online
- **Languages**: English/Uzbek interface support
- **Currency**: UZS (Uzbek Som)

## TECHNICAL ARCHITECTURE

### Core Technology Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript for type safety
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js v5 (Auth.js)
- **UI Framework**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand + React Query (TanStack Query)
- **Deployment**: Vercel (optimized for serverless)
- **Development**: `npm run dev` ready setup

### Database Configuration
```
PostgreSQL Connection:
postgresql://crm_owner:<EMAIL>/crm?sslmode=require
```

### Project Structure
```
inno-crm/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── (dashboard)/       # Protected dashboard routes
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable UI components
├── lib/                   # Utilities and configurations
├── prisma/               # Database schema and migrations
├── public/               # Static assets
├── types/                # TypeScript type definitions
└── package.json          # Dependencies and scripts
```

## CORE FEATURES & MODULES

### 1. Landing Page & Lead Capture
**Public Landing Page (`app/page.tsx`):**
- Modern, responsive design showcasing Innovative Centre
- Hero section with course offerings and testimonials
- Lead capture form (Name, Phone, Course Preference)
- Course information and pricing
- Contact information and location
- Login button in header for staff access

**Lead Management API (`app/api/leads/`):**
- POST `/api/leads` - Capture new leads from landing page
- GET `/api/leads` - Retrieve leads for call center
- PUT `/api/leads/[id]` - Update lead status and notes
- Real-time lead notifications to call center dashboard

### 2. Authentication System
**NextAuth.js v5 Implementation:**
- Role-based authentication (Admin, Manager, Teacher, Student, Academic Manager)
- Secure session management
- Protected routes with middleware
- Login/logout functionality
- Password reset via SMS (no email dependency)

**User Roles & Permissions:**
- **Admin**: Full system access
- **Manager**: Operations and reporting
- **Teacher**: Class and student management
- **Reception**: Lead processing and enrollment
- **Cashier**: Payment processing
- **Student**: Personal dashboard and progress
- **Academic Manager**: Test assignment and statistics viewing

### 3. Dashboard System
**Role-Based Dashboards (`app/(dashboard)/`):**
- **Admin Dashboard**: System overview, analytics, user management
- **Manager Dashboard**: Operations metrics, staff performance
- **Teacher Dashboard**: Classes, students, attendance
- **Reception Dashboard**: Lead management, enrollment
- **Student Dashboard**: Schedule, progress, payments
- **Parent Dashboard**: Child's progress and communication

### 4. Student Management
**Student Profiles & Lifecycle:**
- Comprehensive student information management
- Academic progress tracking (A1 → A2 → B1 → B2 → C1 → C2)
- Placement test results and level assignments
- Course enrollment and group assignments
- Attendance tracking and performance analytics
- Parent communication and updates

**Student Features:**
- Personal information and emergency contacts
- Academic history and certificates
- Payment status and history
- Schedule and class information
- Progress reports and assessments
- Direct messaging with teachers

### 5. Groups & Class Management
**Enhanced Groups System:**
- Group creation with capacity limits (12-30 students)
- Student assignment and management
- Class scheduling with conflict detection
- Teacher assignment and substitutions
- Room/cabinet booking system
- Group performance analytics

**Course Catalog:**
- General English (A1, A2, B1, B2, C1, C2)
- IELTS Preparation (5.5, 6.0, 6.5, 7.0+ bands)
- SAT Preparation
- Math courses
- Kids English (age-based groups)
- Business English
- Conversation clubs

### 6. Assessment & Testing System
**Placement Testing:**
- Online placement tests for new students
- Multi-skill assessment (Reading, Writing, Listening, Speaking)
- Automated scoring and level determination
- Test history and retake management
- Certificate generation

**Progress Testing:**
- Level-up assessments (A1→A2, A2→B1, etc.)
- Monthly progress evaluations
- IELTS mock tests
- Custom teacher assessments
- Performance analytics and reporting

### 7. Payment & Financial Management
**Payment Processing:**
- Multiple payment methods (Cash, UzCard, Humo, Payme, Click)
- Installment plans and payment schedules
- QR code payments
- Payment reminders via SMS
- Refund processing

**Financial Reporting:**
- Revenue tracking by course and teacher
- Payment status dashboard
- Outstanding balances
- Monthly/quarterly reports
- Teacher salary management

### 8. Communication System
**Multi-Channel Communication:**
- In-app notifications and messaging
- SMS integration (Eskiz.uz, MyTelecom.uz)
- WhatsApp Business integration
- Telegram Bot for announcements
- Real-time updates and alerts

**Parent Portal:**
- Child's progress monitoring
- Class schedules and updates
- Payment status
- Direct teacher communication
- Homework tracking
- Certificate downloads

## NEXT.JS IMPLEMENTATION DETAILS

### Package.json Dependencies
```json
{
  "name": "inno-crm",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "db:push": "prisma db push",
    "db:studio": "prisma studio",
    "db:generate": "prisma generate"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@prisma/client": "^5.6.0",
    "next-auth": "5.0.0-beta.4",
    "@auth/prisma-adapter": "^1.0.6",
    "prisma": "^5.6.0",
    "tailwindcss": "^3.3.6",
    "@tailwindcss/forms": "^0.5.7",
    "shadcn-ui": "latest",
    "zustand": "^4.4.6",
    "@tanstack/react-query": "^5.8.4",
    "react-hook-form": "^7.47.0",
    "@hookform/resolvers": "^3.3.2",
    "zod": "^3.22.4",
    "lucide-react": "^0.294.0",
    "recharts": "^2.8.0",
    "date-fns": "^2.30.0"
  },
  "devDependencies": {
    "typescript": "^5.2.2",
    "@types/node": "^20.8.10",
    "@types/react": "^18.2.37",
    "@types/react-dom": "^18.2.15",
    "eslint": "^8.53.0",
    "eslint-config-next": "14.0.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.31"
  }
}
```

### Environment Variables (.env.local)
```bash
# Database
DATABASE_URL="postgresql://inno-crm_owner:<EMAIL>/inno-crm?sslmode=require"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# SMS Providers
ESKIZ_EMAIL="your-email"
ESKIZ_PASSWORD="your-password"

# File Upload
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
```

### Prisma Schema (prisma/schema.prisma)
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  phone     String   @unique
  name      String
  role      Role     @default(STUDENT)
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  accounts     Account[]
  sessions     Session[]
  studentProfile Student?
  teacherProfile Teacher?

  @@map("users")
}

model Student {
  id          String   @id @default(cuid())
  userId      String   @unique
  level       Level    @default(A1)
  branch      String
  emergencyContact String?
  photoUrl    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  enrollments Enrollment[]
  payments    Payment[]

  @@map("students")
}

model Lead {
  id            String     @id @default(cuid())
  name          String
  phone         String
  coursePreference String
  status        LeadStatus @default(NEW)
  source        String?
  notes         String?
  assignedTo    String?
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  @@map("leads")
}

enum Role {
  ADMIN
  MANAGER
  TEACHER
  RECEPTION
  CASHIER
  STUDENT
  PARENT
}

enum Level {
  A1
  A2
  B1
  B2
  IELTS
  SAT
  MATH
  KIDS
}

enum LeadStatus {
  NEW
  CONTACTED
  INTERESTED
  ENROLLED
  NOT_INTERESTED
}
```

### API Routes Structure
```
app/api/
├── auth/                 # NextAuth.js routes
├── leads/
│   ├── route.ts         # GET, POST /api/leads
│   └── [id]/route.ts    # GET, PUT, DELETE /api/leads/[id]
├── students/
│   ├── route.ts         # GET, POST /api/students
│   └── [id]/route.ts    # GET, PUT, DELETE /api/students/[id]
├── groups/
│   ├── route.ts         # GET, POST /api/groups
│   └── [id]/route.ts    # GET, PUT, DELETE /api/groups/[id]
├── payments/
│   ├── route.ts         # GET, POST /api/payments
│   └── [id]/route.ts    # GET, PUT, DELETE /api/payments/[id]
└── analytics/
    └── route.ts         # GET /api/analytics
```

### Component Structure
```
components/
├── ui/                  # shadcn/ui components
│   ├── button.tsx
│   ├── input.tsx
│   ├── card.tsx
│   └── ...
├── forms/
│   ├── lead-form.tsx
│   ├── student-form.tsx
│   └── login-form.tsx
├── dashboard/
│   ├── sidebar.tsx
│   ├── header.tsx
│   └── stats-cards.tsx
├── tables/
│   ├── students-table.tsx
│   ├── leads-table.tsx
│   └── groups-table.tsx
└── charts/
    ├── enrollment-chart.tsx
    └── revenue-chart.tsx
```

## DEVELOPMENT SETUP & DEPLOYMENT

### Initial Setup Commands
```bash
# Clone or initialize the project
npx create-next-app@latest inno-crm --typescript --tailwind --eslint --app

# Navigate to project directory
cd inno-crm

# Install additional dependencies
npm install @prisma/client prisma next-auth @auth/prisma-adapter
npm install zustand @tanstack/react-query react-hook-form @hookform/resolvers zod
npm install lucide-react recharts date-fns
npm install -D @types/node

# Initialize Prisma
npx prisma init

# Setup shadcn/ui
npx shadcn-ui@latest init

# Generate Prisma client
npx prisma generate

# Push database schema
npx prisma db push

# Start development server
npm run dev
```

### Vercel Deployment Configuration
**vercel.json:**
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "DATABASE_URL": "@database_url",
    "NEXTAUTH_SECRET": "@nextauth_secret",
    "NEXTAUTH_URL": "@nextauth_url"
  }
}
```

### Key Implementation Features

#### 1. Landing Page (app/page.tsx)
- Hero section with Innovative Centre branding
- Course showcase with pricing
- Lead capture form (Name, Phone, Course Preference)
- Testimonials and success stories
- Contact information and location
- Login button for staff access

#### 2. Authentication (app/(auth)/)
- NextAuth.js v5 with credentials provider
- Role-based access control
- Protected routes with middleware
- SMS-based password reset
- Session management

#### 3. Dashboard System (app/(dashboard)/)
- Role-specific dashboards
- Real-time analytics and metrics
- Responsive sidebar navigation
- Quick action buttons
- Notification center

#### 4. API Routes (app/api/)
- RESTful API design
- Type-safe with TypeScript
- Error handling and validation
- Rate limiting and security
- Real-time updates

#### 5. Database Integration
- Prisma ORM with PostgreSQL
- Type-safe database queries
- Automated migrations
- Connection pooling
- Data validation

### Performance Optimizations

#### Next.js Specific Optimizations
- **App Router**: Latest Next.js routing system
- **Server Components**: Reduce client-side JavaScript
- **Image Optimization**: Next.js Image component
- **Font Optimization**: Next.js Font optimization
- **Bundle Analysis**: Analyze and optimize bundle size
- **Caching**: Implement proper caching strategies

#### Database Optimizations
- **Indexing**: Proper database indexes
- **Query Optimization**: Efficient Prisma queries
- **Connection Pooling**: Optimize database connections
- **Data Pagination**: Implement pagination for large datasets

#### UI/UX Optimizations
- **Lazy Loading**: Component lazy loading
- **Code Splitting**: Route-based code splitting
- **Progressive Loading**: Skeleton screens and loading states
- **Mobile First**: Responsive design approach

### Security Implementation

#### Authentication & Authorization
- **JWT Tokens**: Secure token-based authentication
- **Role-Based Access**: Granular permission system
- **Session Security**: Secure session management
- **Password Security**: Bcrypt password hashing

#### Data Protection
- **Input Validation**: Zod schema validation
- **SQL Injection Prevention**: Prisma ORM protection
- **XSS Prevention**: React built-in protection
- **CSRF Protection**: NextAuth.js CSRF protection

#### API Security
- **Rate Limiting**: Prevent API abuse
- **CORS Configuration**: Proper CORS setup
- **Environment Variables**: Secure configuration
- **Error Handling**: Secure error responses

### Testing Strategy

#### Unit Testing
- **Jest**: JavaScript testing framework
- **React Testing Library**: Component testing
- **Prisma Testing**: Database testing
- **API Testing**: Route handler testing

#### Integration Testing
- **Cypress**: End-to-end testing
- **Database Testing**: Integration with test database
- **Authentication Testing**: Login flow testing
- **API Integration**: Full API testing

### Monitoring & Analytics

#### Performance Monitoring
- **Vercel Analytics**: Built-in performance monitoring
- **Core Web Vitals**: Performance metrics
- **Error Tracking**: Error monitoring and reporting
- **Database Monitoring**: Query performance tracking

#### Business Analytics
- **User Analytics**: User behavior tracking
- **Conversion Tracking**: Lead to enrollment conversion
- **Performance Metrics**: System performance KPIs
- **Custom Dashboards**: Business intelligence dashboards
