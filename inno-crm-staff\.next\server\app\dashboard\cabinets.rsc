1:"$Sreact.fragment"
2:I[2044,["2108","static/chunks/2108-01f45b4bd0af2163.js","1603","static/chunks/1603-e114a4e36a8b775d.js","7177","static/chunks/app/layout-617fbcf76a0e5f3c.js"],"QueryProvider"]
3:I[8230,["2108","static/chunks/2108-01f45b4bd0af2163.js","1603","static/chunks/1603-e114a4e36a8b775d.js","7177","static/chunks/app/layout-617fbcf76a0e5f3c.js"],"AuthProvider"]
4:I[7555,[],""]
5:I[1295,[],""]
6:I[7705,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","6874","static/chunks/6874-1571340a9900ccc2.js","2108","static/chunks/2108-01f45b4bd0af2163.js","9526","static/chunks/9526-48bf5e0dc984c543.js","2198","static/chunks/2198-a256aef0768b8d69.js","9305","static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js"],"BranchProvider"]
7:I[5248,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","6874","static/chunks/6874-1571340a9900ccc2.js","2108","static/chunks/2108-01f45b4bd0af2163.js","9526","static/chunks/9526-48bf5e0dc984c543.js","2198","static/chunks/2198-a256aef0768b8d69.js","9305","static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js"],"Sidebar"]
8:I[2871,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","6874","static/chunks/6874-1571340a9900ccc2.js","2108","static/chunks/2108-01f45b4bd0af2163.js","9526","static/chunks/9526-48bf5e0dc984c543.js","2198","static/chunks/2198-a256aef0768b8d69.js","9305","static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js"],"Header"]
9:I[9800,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","6874","static/chunks/6874-1571340a9900ccc2.js","2108","static/chunks/2108-01f45b4bd0af2163.js","9526","static/chunks/9526-48bf5e0dc984c543.js","2198","static/chunks/2198-a256aef0768b8d69.js","9305","static/chunks/app/(dashboard)/layout-4a85d21149a1e87d.js"],"Toaster"]
a:I[894,[],"ClientPageRoot"]
b:I[5673,["5003","static/chunks/5003-f1c951693ce1da97.js","6221","static/chunks/6221-af8dd20f8eba536a.js","4358","static/chunks/4358-5cb69af792433a85.js","1071","static/chunks/1071-1406b437091ae070.js","2356","static/chunks/2356-a26e8079efae81ef.js","6874","static/chunks/6874-1571340a9900ccc2.js","7968","static/chunks/7968-1b65033e19a03161.js","1779","static/chunks/app/(dashboard)/dashboard/cabinets/page-a08cc4863737f8f6.js"],"default"]
e:I[9665,[],"MetadataBoundary"]
10:I[9665,[],"OutletBoundary"]
13:I[4911,[],"AsyncMetadataOutlet"]
15:I[9665,[],"ViewportBoundary"]
17:I[6614,[],""]
:HL["/_next/static/css/fd894b6b769b40d1.css","style"]
0:{"P":null,"b":"LXffJSfHYck5VD0io_kNs","p":"","c":["","dashboard","cabinets"],"i":false,"f":[[["",{"children":["(dashboard)",{"children":["dashboard",{"children":["cabinets",{"children":["__PAGE__",{}]}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/fd894b6b769b40d1.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["(dashboard)",["$","$1","c",{"children":[null,["$","$L6",null,{"children":["$","div",null,{"className":"flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50","children":[["$","$L7",null,{}],["$","div",null,{"className":"flex-1 flex flex-col overflow-hidden","children":[["$","$L8",null,{}],["$","main",null,{"className":"flex-1 overflow-x-hidden overflow-y-auto bg-transparent p-6 lg:p-8","children":["$","div",null,{"className":"max-w-7xl mx-auto","children":["$","div",null,{"className":"fade-in","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:style","children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style","children":404}],["$","div",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style","children":["$","h2",null,{"style":"$0:f:0:1:1:props:children:1:props:children:props:children:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style","children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],["$","$L9",null,{}]]}]}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["cabinets",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$La",null,{"Component":"$b","searchParams":{},"params":{},"promises":["$@c","$@d"]}],["$","$Le",null,{"children":"$Lf"}],null,["$","$L10",null,{"children":["$L11","$L12",["$","$L13",null,{"promise":"$@14"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","Ob757KNypHH3p0k3dTuqP",{"children":[["$","$L15",null,{"children":"$L16"}],null]}],null]}],false]],"m":"$undefined","G":["$17","$undefined"],"s":false,"S":true}
18:"$Sreact.suspense"
19:I[4911,[],"AsyncMetadata"]
c:{}
d:{}
f:["$","$18",null,{"fallback":null,"children":["$","$L19",null,{"promise":"$@1a"}]}]
12:null
16:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
11:null
1a:{"metadata":[["$","title","0",{"children":"Innovative Centre CRM"}],["$","meta","1",{"name":"description","content":"Customer Relationship Management System for Innovative Centre"}]],"error":null,"digest":"$undefined"}
14:{"metadata":"$1a:metadata","error":null,"digest":"$undefined"}
