"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9526],{2318:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2895).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2915:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3109:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2895).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},4576:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2895).A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},5040:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2895).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},7580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7655:(e,t,r)=>{r.d(t,{LM:()=>G,OK:()=>K,VM:()=>E,bL:()=>B,lr:()=>M});var n=r(2115),o=r(3655),l=r(8905),i=r(6081),a=r(6101),s=r(9033),c=r(4315),d=r(2712),u=r(9367),p=r(5185),f=r(5155),h="ScrollArea",[v,w]=(0,i.A)(h),[m,y]=v(h),g=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:l="hover",dir:i,scrollHideDelay:s=600,...d}=e,[u,p]=n.useState(null),[h,v]=n.useState(null),[w,y]=n.useState(null),[g,b]=n.useState(null),[S,x]=n.useState(null),[E,T]=n.useState(0),[C,R]=n.useState(0),[A,N]=n.useState(!1),[L,k]=n.useState(!1),P=(0,a.s)(t,e=>p(e)),_=(0,c.jH)(i);return(0,f.jsx)(m,{scope:r,type:l,dir:_,scrollHideDelay:s,scrollArea:u,viewport:h,onViewportChange:v,content:w,onContentChange:y,scrollbarX:g,onScrollbarXChange:b,scrollbarXEnabled:A,onScrollbarXEnabledChange:N,scrollbarY:S,onScrollbarYChange:x,scrollbarYEnabled:L,onScrollbarYEnabledChange:k,onCornerWidthChange:T,onCornerHeightChange:R,children:(0,f.jsx)(o.sG.div,{dir:_,...d,ref:P,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});g.displayName=h;var b="ScrollAreaViewport",S=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:l,nonce:i,...s}=e,c=y(b,r),d=n.useRef(null),u=(0,a.s)(t,d,c.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,f.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:u,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});S.displayName=b;var x="ScrollAreaScrollbar",E=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,f.jsx)(T,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,f.jsx)(C,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,f.jsx)(R,{...o,ref:t,forceMount:r}):"always"===l.type?(0,f.jsx)(A,{...o,ref:t}):null});E.displayName=x;var T=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=y(x,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,f.jsx)(l.C,{present:r||a,children:(0,f.jsx)(R,{"data-state":a?"visible":"hidden",...o,ref:t})})}),C=n.forwardRef((e,t)=>{var r,o;let{forceMount:i,...a}=e,s=y(x,e.__scopeScrollArea),c="horizontal"===e.orientation,d=V(()=>h("SCROLL_END"),100),[u,h]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let r=o[e][t];return null!=r?r:e},r));return n.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>h("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,s.scrollHideDelay,h]),n.useEffect(()=>{let e=s.viewport,t=c?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(h("SCROLL"),d()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,c,h,d]),(0,f.jsx)(l.C,{present:i||"hidden"!==u,children:(0,f.jsx)(A,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:t,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),R=n.forwardRef((e,t)=>{let r=y(x,e.__scopeScrollArea),{forceMount:o,...i}=e,[a,s]=n.useState(!1),c="horizontal"===e.orientation,d=V(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(c?e:t)}},10);return q(r.viewport,d),q(r.content,d),(0,f.jsx)(l.C,{present:o||a,children:(0,f.jsx)(A,{"data-state":a?"visible":"hidden",...i,ref:t})})}),A=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=y(x,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=z(s.viewport,s.content),u={...o,sizes:s,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=W(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return Y([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,f.jsx)(N,{...u,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,f.jsx)(L,{...u,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),N=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=y(x,e.__scopeScrollArea),[s,c]=n.useState(),d=n.useRef(null),u=(0,a.s)(t,d,i.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,f.jsx)(_,{"data-orientation":"horizontal",...l,ref:u,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":W(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:H(s.paddingLeft),paddingEnd:H(s.paddingRight)}})}})}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=y(x,e.__scopeScrollArea),[s,c]=n.useState(),d=n.useRef(null),u=(0,a.s)(t,d,i.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,f.jsx)(_,{"data-orientation":"vertical",...l,ref:u,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":W(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:H(s.paddingTop),paddingEnd:H(s.paddingBottom)}})}})}),[k,P]=v(x),_=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:l,hasThumb:i,onThumbChange:c,onThumbPointerUp:d,onThumbPointerDown:u,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:w,onResize:m,...g}=e,b=y(x,r),[S,E]=n.useState(null),T=(0,a.s)(t,e=>E(e)),C=n.useRef(null),R=n.useRef(""),A=b.viewport,N=l.content-l.viewport,L=(0,s.c)(w),P=(0,s.c)(h),_=V(m,10);function D(e){C.current&&v({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==S?void 0:S.contains(t))&&L(e,N)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[A,S,N,L]),n.useEffect(P,[l,P]),q(S,_),q(b.content,_),(0,f.jsx)(k,{scope:r,scrollbar:S,hasThumb:i,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(d),onThumbPositionChange:P,onThumbPointerDown:(0,s.c)(u),children:(0,f.jsx)(o.sG.div,{...g,ref:T,style:{position:"absolute",...g.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=S.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:(0,p.m)(e.onPointerMove,D),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,b.viewport&&(b.viewport.style.scrollBehavior=""),C.current=null})})})}),D="ScrollAreaThumb",M=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=P(D,e.__scopeScrollArea);return(0,f.jsx)(l.C,{present:r||o.hasThumb,children:(0,f.jsx)(j,{ref:t,...n})})}),j=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:l,...i}=e,s=y(D,r),c=P(D,r),{onThumbPositionChange:d}=c,u=(0,a.s)(t,e=>c.onThumbChange(e)),h=n.useRef(void 0),v=V(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{v(),h.current||(h.current=F(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,v,d]),(0,f.jsx)(o.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...i,ref:u,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,p.m)(e.onPointerUp,c.onThumbPointerUp)})});M.displayName=D;var O="ScrollAreaCorner",U=n.forwardRef((e,t)=>{let r=y(O,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,f.jsx)(I,{...e,ref:t}):null});U.displayName=O;var I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=y(O,r),[a,s]=n.useState(0),[c,d]=n.useState(0),u=!!(a&&c);return q(i.scrollbarX,()=>{var e;let t=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),d(t)}),q(i.scrollbarY,()=>{var e;let t=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),u?(0,f.jsx)(o.sG.div,{...l,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function H(e){return e?parseInt(e,10):0}function z(e,t){let r=e/t;return isNaN(r)?0:r}function W(e){let t=z(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function X(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=W(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,u.q)(e,"ltr"===r?[0,i]:[-1*i,0]);return Y([0,i],[0,l-n])(a)}function Y(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var F=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function V(e,t){let r=(0,s.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function q(e,t){let r=(0,s.c)(t);(0,d.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var B=g,G=S,K=U},8905:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(2115),o=r(6101),l=r(2712),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),s=n.useRef(null),c=n.useRef(e),d=n.useRef("none"),[u,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=a(s.current);d.current="mounted"===u?e:"none"},[u]),(0,l.N)(()=>{let t=s.current,r=c.current;if(r!==e){let n=d.current,o=a(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):r&&n!==o?p("ANIMATION_OUT"):p("UNMOUNT"),c.current=e}},[e,p]),(0,l.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=a(s.current).includes(e.animationName);if(e.target===o&&n&&(p("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(d.current=a(s.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{s.current=e?getComputedStyle(e):null,i(e)},[])}}(t),s="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),c=(0,o.s)(i.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof r||i.isPresent?n.cloneElement(s,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},9074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9397:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(2895).A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])}}]);