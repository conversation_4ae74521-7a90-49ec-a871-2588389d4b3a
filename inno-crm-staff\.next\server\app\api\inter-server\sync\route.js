(()=>{var e={};e.id=7174,e.ids=[7174],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,t,r)=>{"use strict";function s(e){let t=e.headers.get("X-Inter-Server-Secret"),r=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!t||!s||t!==s)return!1;if(r){let e=parseInt(r),t=Date.now();if(isNaN(e)||t-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function a(e,t){try{let r="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!r)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${r}${t.endpoint}`,a={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let t=Date.now().toString(),r=o.getServerConfig(),s=`${r.serverType}-${t}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":r.serverType,"X-Request-ID":s,"X-Timestamp":t,"User-Agent":`${r.serverType}-server`}}(),...t.headers},n=await fetch(s,{method:t.method,headers:a,body:t.data?JSON.stringify(t.data):void 0}),i=await n.json();return{success:n.ok,data:i,status:n.status,error:n.ok?void 0:i.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}r.d(t,{LQ:()=>o,cU:()=>n,g2:()=>s});class n{static async authenticateUser(e,t){return a("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:t}})}static async getUserData(e){return a("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,t){return a("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:t}})}}class o{static logRequest(e,t,r,s){let a=new Date().toISOString(),n=process.env.SERVER_TYPE||"unknown";console.log(`[${a}] Inter-Server ${e.toUpperCase()}: ${t}`,{server:n,success:r,details:s})}static async healthCheck(e){try{return(await a(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47119:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>y,serverHooks:()=>m,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>R,POST:()=>d});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),c=r(7786),u=r(79464);async function d(e){try{let t;if(!(0,c.g2)(e))return c.LQ.logRequest("incoming","/api/inter-server/sync",!1,"Unauthorized"),i.NextResponse.json({error:"Unauthorized inter-server request"},{status:401});let{type:r,data:s,operation:a="sync"}=await e.json();if(!r||!s)return i.NextResponse.json({error:"Type and data are required"},{status:400});switch(r){case"user":t=await p(s,a);break;case"student":t=await l(s,a);break;case"lead":t=await w(s,a);break;case"enrollment":t=await h(s,a);break;case"attendance":t=await v(s,a);break;default:return i.NextResponse.json({error:`Unsupported sync type: ${r}`},{status:400})}return c.LQ.logRequest("incoming","/api/inter-server/sync",!0,{type:r,operation:a}),i.NextResponse.json({success:!0,type:r,operation:a,result:t,timestamp:new Date().toISOString()})}catch(e){return console.error("Inter-server sync error:",e),c.LQ.logRequest("incoming","/api/inter-server/sync",!1,e),i.NextResponse.json({error:"Data synchronization failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e,t){switch(t){case"create":return await u.z.user.create({data:e});case"update":return await u.z.user.update({where:{id:e.id},data:e});case"sync":return await u.z.user.upsert({where:{id:e.id},create:e,update:e});default:throw Error(`Unsupported user operation: ${t}`)}}async function l(e,t){switch(t){case"create":return await u.z.student.create({data:e});case"update":return await u.z.student.update({where:{id:e.id},data:e});case"sync":return await u.z.student.upsert({where:{id:e.id},create:e,update:e});default:throw Error(`Unsupported student operation: ${t}`)}}async function w(e,t){switch(t){case"create":return await u.z.lead.create({data:e});case"update":return await u.z.lead.update({where:{id:e.id},data:e});case"sync":return await u.z.lead.upsert({where:{id:e.id},create:e,update:e});default:throw Error(`Unsupported lead operation: ${t}`)}}async function h(e,t){switch(t){case"create":return await u.z.enrollment.create({data:e});case"update":return await u.z.enrollment.update({where:{id:e.id},data:e});case"sync":return await u.z.enrollment.upsert({where:{id:e.id},create:e,update:e});default:throw Error(`Unsupported enrollment operation: ${t}`)}}async function v(e,t){switch(t){case"create":return await u.z.attendance.create({data:e});case"update":return await u.z.attendance.update({where:{id:e.id},data:e});case"sync":return await u.z.attendance.upsert({where:{id:e.id},create:e,update:e});default:throw Error(`Unsupported attendance operation: ${t}`)}}async function R(e){try{if(!(0,c.g2)(e))return i.NextResponse.json({error:"Unauthorized inter-server request"},{status:401});let t={lastSync:new Date().toISOString(),server:process.env.SERVER_TYPE,syncEndpoints:["user","student","lead","enrollment","attendance"],supportedOperations:["create","update","sync"]};return i.NextResponse.json(t)}catch(e){return i.NextResponse.json({error:"Failed to get sync status"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/inter-server/sync/route",pathname:"/api/inter-server/sync",filename:"route",bundlePath:"app/api/inter-server/sync/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\inter-server\\sync\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:f,serverHooks:m}=y;function S(){return(0,o.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:f})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580],()=>r(47119));module.exports=s})();