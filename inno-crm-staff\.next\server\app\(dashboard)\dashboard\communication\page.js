(()=>{var e={};e.id=8750,e.ids=[8750],e.modules={3018:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>d,TN:()=>c});var t=a(60687),r=a(43210),i=a(24224),n=a(96241);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef(({className:e,variant:s,...a},r)=>(0,t.jsx)("div",{ref:r,role:"alert",className:(0,n.cn)(l({variant:s}),e),...a}));d.displayName="Alert",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...s}));c.displayName="AlertDescription"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26269:(e,s,a)=>{"use strict";a.d(s,{tU:()=>T,av:()=>W,j7:()=>P,Xi:()=>M});var t=a(60687),r=a(43210),i=a(70569),n=a(11273),l=a(72942),d=a(46059),c=a(14163),o=a(43),m=a(65551),x=a(96963),u="Tabs",[h,p]=(0,n.A)(u,[l.RG]),f=(0,l.RG)(),[j,v]=h(u),b=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:d,activationMode:h="automatic",...p}=e,f=(0,o.jH)(d),[v,b]=(0,m.i)({prop:r,onChange:i,defaultProp:n??"",caller:u});return(0,t.jsx)(j,{scope:a,baseId:(0,x.B)(),value:v,onValueChange:b,orientation:l,dir:f,activationMode:h,children:(0,t.jsx)(c.sG.div,{dir:f,"data-orientation":l,...p,ref:s})})});b.displayName=u;var g="TabsList",N=r.forwardRef((e,s)=>{let{__scopeTabs:a,loop:r=!0,...i}=e,n=v(g,a),d=f(a);return(0,t.jsx)(l.bL,{asChild:!0,...d,orientation:n.orientation,dir:n.dir,loop:r,children:(0,t.jsx)(c.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:s})})});N.displayName=g;var y="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,disabled:n=!1,...d}=e,o=v(y,a),m=f(a),x=A(o.baseId,r),u=S(o.baseId,r),h=r===o.value;return(0,t.jsx)(l.q7,{asChild:!0,...m,focusable:!n,active:h,children:(0,t.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":u,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:x,...d,ref:s,onMouseDown:(0,i.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;h||n||!e||o.onValueChange(r)})})})});w.displayName=y;var k="TabsContent",C=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:i,forceMount:n,children:l,...o}=e,m=v(k,a),x=A(m.baseId,i),u=S(m.baseId,i),h=i===m.value,p=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(d.C,{present:n||h,children:({present:a})=>(0,t.jsx)(c.sG.div,{"data-state":h?"active":"inactive","data-orientation":m.orientation,role:"tabpanel","aria-labelledby":x,hidden:!a,id:u,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})})});function A(e,s){return`${e}-trigger-${s}`}function S(e,s){return`${e}-content-${s}`}C.displayName=k;var R=a(96241);let T=b,P=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(N,{ref:a,className:(0,R.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));P.displayName=N.displayName;let M=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(w,{ref:a,className:(0,R.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));M.displayName=w.displayName;let W=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(C,{ref:a,className:(0,R.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));W.displayName=C.displayName},27900:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39021:(e,s,a)=>{Promise.resolve().then(a.bind(a,45245))},41550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},45245:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\communication\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\page.tsx","default")},50834:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(60687),r=a(43210),i=a(55192),n=a(2553),l=a(59821),d=a(63974),c=a(26269),o=a(3018),m=a(58887),x=a(48340),u=a(41550),h=a(97051),p=a(27900),f=a(41312);let j=(0,a(18962).A)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);var v=a(23689),b=a(83281),g=a(84027);function N(){let[e,s]=(0,r.useState)("sms"),[a,N]=(0,r.useState)(""),[y,w]=(0,r.useState)(""),[k,C]=(0,r.useState)([]),[A,S]=(0,r.useState)({sms:!1,email:!1}),[R,T]=(0,r.useState)(!1),[P,M]=(0,r.useState)(""),[W,E]=(0,r.useState)("reminder"),[D,Z]=(0,r.useState)("medium"),[B,z]=(0,r.useState)(["email"]),[q,_]=(0,r.useState)([{label:"SMS Sent Today",value:"0",icon:m.A,color:"text-blue-600"},{label:"Calls Made",value:"0",icon:x.A,color:"text-green-600"},{label:"Emails Sent",value:"0",icon:u.A,color:"text-purple-600"},{label:"Active Notifications",value:"0",icon:h.A,color:"text-orange-600"}]),U=async()=>{try{let e=await fetch("/api/workflows?action=list");if(e.ok){let s=await e.json();C(s.workflows)}}catch(e){console.error("Error fetching workflows:",e)}},$=async(e,s)=>{try{(await fetch("/api/workflows",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({workflowId:e,enabled:s})})).ok&&(U(),M(`Workflow ${s?"enabled":"disabled"} successfully`))}catch(e){M("Failed to update workflow")}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex justify-between items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Communication Center"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Send messages, manage workflows, and configure notifications"})]})}),P&&(0,t.jsx)(o.Fc,{children:(0,t.jsx)(o.TN,{children:P})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:q.map((e,s)=>(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(e.icon,{className:`h-8 w-8 ${e.color}`}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value})]})]})})},s))}),(0,t.jsxs)(c.tU,{defaultValue:"send",className:"space-y-6",children:[(0,t.jsxs)(c.j7,{children:[(0,t.jsx)(c.Xi,{value:"send",children:"Send Messages"}),(0,t.jsx)(c.Xi,{value:"workflows",children:"Workflows"}),(0,t.jsx)(c.Xi,{value:"settings",children:"Settings"})]}),(0,t.jsx)(c.av,{value:"send",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Send Message"}),(0,t.jsx)(i.BT,{children:"Compose and send messages to students or groups"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"flex space-x-2",children:["sms","whatsapp","email","notification"].map(a=>(0,t.jsx)(n.$,{variant:e===a?"default":"outline",onClick:()=>s(a),size:"sm",children:a.toUpperCase()},a))}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Recipient"}),(0,t.jsxs)(d.l6,{value:y,onValueChange:w,children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"Select recipient"})}),(0,t.jsxs)(d.gC,{children:[(0,t.jsx)(d.eb,{value:"all-students",children:"All Students"}),(0,t.jsx)(d.eb,{value:"group-a1",children:"Group A1 - Morning"}),(0,t.jsx)(d.eb,{value:"group-b1",children:"Group B1 - Evening"}),(0,t.jsx)(d.eb,{value:"ielts-students",children:"IELTS Students"}),(0,t.jsx)(d.eb,{value:"individual",children:"Individual Student"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Message"}),(0,t.jsx)("textarea",{className:"w-full p-3 border border-gray-300 rounded-md resize-none",rows:4,placeholder:"Type your message here...",value:a,onChange:e=>N(e.target.value)}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[a.length,"/160 characters"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",id:"schedule",className:"rounded"}),(0,t.jsx)("label",{htmlFor:"schedule",className:"text-sm",children:"Schedule for later"})]}),(0,t.jsxs)(n.$,{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Send Message"})]})]})]})]}),(0,t.jsxs)(i.Zp,{className:"mt-6",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Recent Messages"}),(0,t.jsx)(i.BT,{children:"Latest sent messages and their status"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:[{id:1,type:"SMS",recipient:"Aziza Karimova",message:"Reminder: Your IELTS class starts tomorrow at 10:00 AM",status:"Delivered",timestamp:"2 hours ago"},{id:2,type:"WhatsApp",recipient:"Bobur Toshev",message:"Your payment has been received. Thank you!",status:"Read",timestamp:"4 hours ago"},{id:3,type:"SMS",recipient:"Group A1-Morning",message:"Class moved to Room 205 today",status:"Delivered",timestamp:"6 hours ago"}].map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 border rounded-lg",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.recipient}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.E,{variant:"outline",children:e.type}),(0,t.jsx)(l.E,{className:"Delivered"===e.status?"bg-green-100 text-green-800":"Read"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800",children:e.status})]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:e.timestamp})]})]},e.id))})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Message Templates"}),(0,t.jsx)(i.BT,{children:"Quick templates for common messages"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",children:[{id:1,name:"Class Reminder",content:"Reminder: Your {course} class starts {time} at {location}"},{id:2,name:"Payment Confirmation",content:"Your payment of {amount} has been received. Thank you!"},{id:3,name:"Welcome Message",content:"Welcome to Innovative Centre! Your journey to English mastery begins now."},{id:4,name:"Assignment Reminder",content:"Don't forget to complete your homework for tomorrow's class."}].map(e=>(0,t.jsxs)("div",{className:"p-3 border rounded-lg hover:bg-gray-50 cursor-pointer",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:e.content}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>N(e.content),children:"Use Template"})]},e.id))})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Quick Actions"}),(0,t.jsx)(i.BT,{children:"Common communication tasks"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Send Class Reminder"]}),(0,t.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Payment Reminder"]}),(0,t.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Welcome New Students"]}),(0,t.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Schedule Follow-up"]})]})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Settings"}),(0,t.jsx)(i.BT,{children:"Communication preferences"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"SMS Notifications"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Email Notifications"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"WhatsApp Integration"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Auto Reminders"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]})]})})]})]})]})}),(0,t.jsx)(c.av,{value:"workflows",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Automated Workflows"}),(0,t.jsx)(i.BT,{children:"Manage automated business processes and notifications"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:k.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(j,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.description}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)(l.E,{variant:"outline",size:"sm",children:e.trigger.event})})]})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.E,{variant:e.enabled?"default":"secondary",children:e.enabled?"Enabled":"Disabled"}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>$(e.id,!e.enabled),children:e.enabled?"Disable":"Enable"})]})]},e.id))})})]})}),(0,t.jsx)(c.av,{value:"settings",children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Communication Settings"}),(0,t.jsx)(i.BT,{children:"Configure SMS and email service providers"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Service Status"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("span",{children:"SMS Service"})]}),A.sms?(0,t.jsx)(v.A,{className:"h-5 w-5 text-green-600"}):(0,t.jsx)(b.A,{className:"h-5 w-5 text-red-600"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsx)("span",{children:"Email Service"})]}),A.email?(0,t.jsx)(v.A,{className:"h-5 w-5 text-green-600"}):(0,t.jsx)(b.A,{className:"h-5 w-5 text-red-600"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Notification Preferences"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"SMS Notifications"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Email Notifications"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Auto Reminders"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Payment Alerts"}),(0,t.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]})]})]}),(0,t.jsxs)(n.$,{children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Save Settings"]})]})})]})})]})]})}},55192:(e,s,a)=>{"use strict";a.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>l});var t=a(60687),r=a(43210),i=a(96241);let n=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));n.displayName="Card";let l=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},62989:(e,s,a)=>{Promise.resolve().then(a.bind(a,50834))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,s,a)=>{"use strict";a.d(s,{bq:()=>x,eb:()=>f,gC:()=>p,l6:()=>o,yv:()=>m});var t=a(60687),r=a(43210),i=a(22670),n=a(78272),l=a(3589),d=a(13964),c=a(96241);let o=i.bL;i.YJ;let m=i.WT,x=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(i.l9,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,(0,t.jsx)(i.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=i.l9.displayName;let u=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(i.PP,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(l.A,{className:"h-4 w-4"})}));u.displayName=i.PP.displayName;let h=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(i.wn,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=i.wn.displayName;let p=r.forwardRef(({className:e,children:s,position:a="popper",...r},n)=>(0,t.jsx)(i.ZL,{children:(0,t.jsxs)(i.UC,{ref:n,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,t.jsx)(u,{}),(0,t.jsx)(i.LM,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,t.jsx)(h,{})]})}));p.displayName=i.UC.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(i.JU,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.JU.displayName;let f=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(i.q7,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(i.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(i.p4,{children:s})]}));f.displayName=i.q7.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(i.wv,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.wv.displayName},83281:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},91919:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=a(65239),r=a(48088),i=a(88170),n=a.n(i),l=a(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(s,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["communication",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,45245)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\communication\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/communication/page",pathname:"/dashboard/communication",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,8706,6631],()=>a(91919));module.exports=t})();