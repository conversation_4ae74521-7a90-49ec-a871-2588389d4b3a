(()=>{var e={};e.id=2416,e.ids=[2416],e.modules={2579:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\[id]\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9923:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21755:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>d.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),n=t(88170),d=t.n(n),l=t(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(s,i);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["students",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2579)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\[id]\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/students/[id]/page",pathname:"/dashboard/students/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41550:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},43381:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(60687),a=t(43210),n=t(16189),d=t(55192),l=t(2553),i=t(59821),c=t(96752),o=t(96241),x=t(23689),m=t(83281),h=t(48730),u=t(63851),j=t(28559),p=t(9923),f=t(58869),y=t(48340),N=t(41550),b=t(97992),g=t(40228),v=t(27351),A=t(85778),w=t(85814),P=t.n(w);function k(){(0,n.useParams)();let[e,s]=(0,a.useState)(null),[t,w]=(0,a.useState)(!0),k=e=>{switch(e){case"ACTIVE":case"PAID":case"PRESENT":return"bg-green-100 text-green-800";case"COMPLETED":case"EXCUSED":return"bg-blue-100 text-blue-800";case"DROPPED":case"DEBT":case"ABSENT":return"bg-red-100 text-red-800";case"SUSPENDED":case"LATE":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},C=e=>{switch(e){case"PRESENT":return(0,r.jsx)(x.A,{className:"h-4 w-4 text-green-600"});case"ABSENT":return(0,r.jsx)(m.A,{className:"h-4 w-4 text-red-600"});case"LATE":return(0,r.jsx)(h.A,{className:"h-4 w-4 text-yellow-600"});case"EXCUSED":return(0,r.jsx)(u.A,{className:"h-4 w-4 text-blue-600"});default:return null}},E=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(e/12500);if(t)return(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:"Loading..."});if(!e)return(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:"Student not found"});let R=e.payments.reduce((e,s)=>e+s.amount,0),T=e.payments.filter(e=>"PAID"===e.status).reduce((e,s)=>e+s.amount,0),D=e.attendances.length,B=e.attendances.filter(e=>"PRESENT"===e.status).length,S=D>0?B/D*100:0;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(P(),{href:"/dashboard/students",children:(0,r.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Back to Students"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:e.user.name}),(0,r.jsx)("p",{className:"text-gray-600",children:"Student Profile"})]})]}),(0,r.jsxs)(l.$,{children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Edit Profile"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsxs)(d.ZB,{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 mr-2"}),"Personal Information"]})}),(0,r.jsxs)(d.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{children:e.user.phone})]}),e.user.email&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{children:e.user.email})]}),e.address&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{children:e.address})]}),e.dateOfBirth&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{children:(0,o.Yq)(e.dateOfBirth)})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)(i.E,{children:e.level})]}),(0,r.jsxs)("div",{className:"pt-2 border-t",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Branch: ",e.branch]}),e.emergencyContact&&(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Emergency: ",e.emergencyContact]})]})]})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsx)(d.ZB,{children:"Academic Overview"})}),(0,r.jsxs)(d.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Active Enrollments"}),(0,r.jsx)("span",{className:"font-semibold",children:e.enrollments.filter(e=>"ACTIVE"===e.status).length})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Total Classes"}),(0,r.jsx)("span",{className:"font-semibold",children:D})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Attendance Rate"}),(0,r.jsxs)("span",{className:`font-semibold ${S>=80?"text-green-600":S>=60?"text-yellow-600":"text-red-600"}`,children:[S.toFixed(1),"%"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Current Level"}),(0,r.jsx)(i.E,{children:e.level})]})]})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsxs)(d.ZB,{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"h-5 w-5 mr-2"}),"Payment Overview"]})}),(0,r.jsxs)(d.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Total Payments"}),(0,r.jsx)("span",{className:"font-semibold",children:E(R)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Paid Amount"}),(0,r.jsx)("span",{className:"font-semibold text-green-600",children:E(T)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Pending Amount"}),(0,r.jsx)("span",{className:"font-semibold text-red-600",children:E(R-T)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Payment Records"}),(0,r.jsx)("span",{className:"font-semibold",children:e.payments.length})]})]})]})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{children:[(0,r.jsx)(d.ZB,{children:"Course Enrollments"}),(0,r.jsx)(d.BT,{children:"Current and past course enrollments"})]}),(0,r.jsx)(d.Wu,{children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{children:(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nd,{children:"Course"}),(0,r.jsx)(c.nd,{children:"Group"}),(0,r.jsx)(c.nd,{children:"Teacher"}),(0,r.jsx)(c.nd,{children:"Duration"}),(0,r.jsx)(c.nd,{children:"Start Date"}),(0,r.jsx)(c.nd,{children:"Status"})]})}),(0,r.jsx)(c.BF,{children:e.enrollments.map(e=>(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.group.course.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Level: ",e.group.course.level]})]})}),(0,r.jsx)(c.nA,{children:e.group.name}),(0,r.jsx)(c.nA,{children:e.group.teacher.user.name}),(0,r.jsxs)(c.nA,{children:[e.group.course.duration," weeks"]}),(0,r.jsx)(c.nA,{children:(0,o.Yq)(e.startDate)}),(0,r.jsx)(c.nA,{children:(0,r.jsx)(i.E,{className:k(e.status),children:e.status})})]},e.id))})]})})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{children:[(0,r.jsx)(d.ZB,{children:"Recent Attendance"}),(0,r.jsx)(d.BT,{children:"Latest attendance records"})]}),(0,r.jsx)(d.Wu,{children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{children:(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nd,{children:"Date"}),(0,r.jsx)(c.nd,{children:"Group"}),(0,r.jsx)(c.nd,{children:"Topic"}),(0,r.jsx)(c.nd,{children:"Status"}),(0,r.jsx)(c.nd,{children:"Notes"})]})}),(0,r.jsx)(c.BF,{children:e.attendances.slice(0,10).map(e=>(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nA,{children:(0,o.Yq)(e.class.date)}),(0,r.jsx)(c.nA,{children:e.class.group.name}),(0,r.jsx)(c.nA,{children:e.class.topic||"No topic"}),(0,r.jsx)(c.nA,{children:(0,r.jsx)(i.E,{className:k(e.status),children:(0,r.jsxs)("div",{className:"flex items-center",children:[C(e.status),(0,r.jsx)("span",{className:"ml-1",children:e.status})]})})}),(0,r.jsx)(c.nA,{children:e.notes||"-"})]},e.id))})]})})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{children:[(0,r.jsx)(d.ZB,{children:"Payment History"}),(0,r.jsx)(d.BT,{children:"Recent payment transactions"})]}),(0,r.jsx)(d.Wu,{children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{children:(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nd,{children:"Date"}),(0,r.jsx)(c.nd,{children:"Amount"}),(0,r.jsx)(c.nd,{children:"Method"}),(0,r.jsx)(c.nd,{children:"Description"}),(0,r.jsx)(c.nd,{children:"Status"})]})}),(0,r.jsx)(c.BF,{children:e.payments.slice(0,10).map(e=>(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nA,{children:(0,o.Yq)(e.createdAt)}),(0,r.jsx)(c.nA,{className:"font-medium",children:E(e.amount)}),(0,r.jsx)(c.nA,{children:e.method}),(0,r.jsx)(c.nA,{children:e.description||"Course payment"}),(0,r.jsx)(c.nA,{children:(0,r.jsx)(i.E,{className:k(e.status),children:e.status})})]},e.id))})]})})]})]})}},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55192:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>d,aR:()=>l});var r=t(60687),a=t(43210),n=t(96241);let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));d.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let i=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));i.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67924:(e,s,t)=>{Promise.resolve().then(t.bind(t,43381))},83281:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},91892:(e,s,t)=>{Promise.resolve().then(t.bind(t,2579))},96752:(e,s,t)=>{"use strict";t.d(s,{A0:()=>l,BF:()=>i,Hj:()=>c,XI:()=>d,nA:()=>x,nd:()=>o});var r=t(60687),a=t(43210),n=t(96241);let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",e),...s})}));d.displayName="Table";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",e),...s}));l.displayName="TableHeader";let i=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...s}));i.displayName="TableBody",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},97992:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,7615,2918,8887,6631],()=>t(21755));module.exports=r})();