(()=>{var e={};e.id=4541,e.ids=[4541],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4716:(e,s,t)=>{Promise.resolve().then(t.bind(t,20792))},9547:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>i});var a=t(65239),r=t(48088),l=t(88170),n=t.n(l),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(s,o);let i={children:["",{children:["(dashboard)",{children:["dashboard",{children:["assessments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78330)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\assessments\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\assessments\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/assessments/page",pathname:"/dashboard/assessments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(60687),r=t(43210),l=t(82136),n=t(55192),d=t(2553),o=t(68988),i=t(59821),c=t(63974),m=t(37826),u=t(96752),x=t(39390),p=t(96241),f=t(23689),h=t(83281),j=t(96474),g=t(41312),b=t(10022),N=t(27351),v=t(86561),y=t(99270),w=t(71702);function C(){let{data:e}=(0,l.useSession)(),[s,t]=(0,r.useState)([]),[C,R]=(0,r.useState)([]),[A,S]=(0,r.useState)(!0),[T,E]=(0,r.useState)(""),[P,D]=(0,r.useState)("all"),[k,_]=(0,r.useState)(!1),[F,I]=(0,r.useState)(null),[L,O]=(0,r.useState)(""),[G,M]=(0,r.useState)(""),[q,U]=(0,r.useState)({}),{toast:W}=(0,w.dj)(),$=["ADMIN","MANAGER","TEACHER"].includes(e?.user?.role),z=(0,r.useCallback)(async()=>{S(!0);try{let e=new URLSearchParams;P&&"all"!==P&&e.append("type",P);let s=await fetch(`/api/assessments?${e}`);if(!s.ok)throw Error("Failed to fetch assessments");let a=await s.json();t(a.assessments||[])}catch(e){W({variant:"destructive",title:"Error",description:"Failed to fetch assessments"})}finally{S(!1)}},[P,W]),B=s.filter(e=>{let s=e.student?.user.name||"",t=e.group?.name||"",a=e.testName||"";return s.toLowerCase().includes(T.toLowerCase())||t.toLowerCase().includes(T.toLowerCase())||a.toLowerCase().includes(T.toLowerCase())||e.type.toLowerCase().includes(T.toLowerCase())}),Z=async e=>{if(e.preventDefault(),!F||!L||!G)return void W({variant:"destructive",title:"Error",description:"Please select a group, enter test name, and select test type"});try{let e=F.enrollments.map(e=>{let s=e.student.id,t=q[s];return t?fetch("/api/assessments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:s,groupId:F.id,testName:L,type:G,level:F.course.level,score:t.score,maxScore:t.maxScore,passed:t.passed,completedAt:new Date().toISOString()})}):null}).filter(Boolean);await Promise.all(e),W({title:"Success",description:`Assessment results recorded for ${e.length} students`}),_(!1),J(),z()}catch(e){W({variant:"destructive",title:"Error",description:"Failed to record assessment results"})}},J=()=>{I(null),O(""),M(""),U({})},V=e=>{switch(e){case"LEVEL_TEST":return"bg-green-100 text-green-800";case"PROGRESS_TEST":return"bg-yellow-100 text-yellow-800";case"FINAL_EXAM":return"bg-red-100 text-red-800";case"GROUP_TEST":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},H=e=>e?(0,a.jsx)(f.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(h.A,{className:"h-5 w-5 text-red-600"}),X=(e,s,t)=>{U(a=>({...a,[e]:{...a[e],[s]:t}}))};return $?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Assessment Recording"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Record post-test results for student groups"})]}),(0,a.jsxs)(m.lG,{open:k,onOpenChange:_,children:[(0,a.jsx)(m.zM,{asChild:!0,children:(0,a.jsxs)(d.$,{onClick:J,children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Record Test Results"]})}),(0,a.jsxs)(m.Cf,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{children:"Record Test Results"}),(0,a.jsx)(m.rr,{children:"Select a group and enter test results for all students."})]}),(0,a.jsxs)("form",{onSubmit:Z,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Step 1: Select Student Group"})]}),(0,a.jsxs)(c.l6,{value:F?.id||"",onValueChange:e=>{I(C.find(s=>s.id===e)||null),U({})},children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select a student group"})}),(0,a.jsx)(c.gC,{children:C.map(e=>(0,a.jsxs)(c.eb,{value:e.id,children:[e.name," - ",e.course.name," (",e.course.level,")"]},e.id))})]})]}),F&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Step 2: Test Information"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:"testName",children:"Test Name *"}),(0,a.jsx)(o.p,{id:"testName",value:L,onChange:e=>O(e.target.value),placeholder:"e.g., Unit 5 Test, Mid-term Exam",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:"testType",children:"Test Type *"}),(0,a.jsxs)(c.l6,{value:G,onValueChange:M,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select test type"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"LEVEL_TEST",children:"Level Test"}),(0,a.jsx)(c.eb,{value:"PROGRESS_TEST",children:"Progress Test"}),(0,a.jsx)(c.eb,{value:"FINAL_EXAM",children:"Final Exam"}),(0,a.jsx)(c.eb,{value:"GROUP_TEST",children:"Group Test"})]})]})]})]})]}),F&&L&&G&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Step 3: Enter Individual Scores"})]}),(0,a.jsx)("div",{className:"border rounded-lg p-4 max-h-60 overflow-y-auto",children:(0,a.jsx)("div",{className:"space-y-3",children:F.enrollments.map(e=>{let s=e.student.id,t=q[s]||{score:0,maxScore:100,passed:!1};return(0,a.jsxs)("div",{className:"grid grid-cols-5 gap-3 items-center p-3 border rounded bg-gray-50",children:[(0,a.jsx)("div",{className:"col-span-2",children:(0,a.jsx)("span",{className:"font-medium",children:e.student.user.name})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:`score-${s}`,className:"text-xs",children:"Score"}),(0,a.jsx)(o.p,{id:`score-${s}`,type:"number",min:"0",value:t.score,onChange:e=>{let a=parseInt(e.target.value)||0,r=a>=.6*t.maxScore;X(s,"score",a),X(s,"passed",r)},className:"h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:`maxScore-${s}`,className:"text-xs",children:"Max Score"}),(0,a.jsx)(o.p,{id:`maxScore-${s}`,type:"number",min:"1",value:t.maxScore,onChange:e=>{let a=parseInt(e.target.value)||100,r=t.score>=.6*a;X(s,"maxScore",a),X(s,"passed",r)},className:"h-8"})]}),(0,a.jsx)("div",{className:"flex items-center justify-center",children:t.passed?(0,a.jsx)(f.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(h.A,{className:"h-5 w-5 text-red-600"})})]},s)})})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:[(0,a.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>_(!1),children:"Cancel"}),(0,a.jsx)(d.$,{type:"submit",disabled:!F||!L||!G||0===Object.keys(q).length,children:"Record Test Results"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Assessments"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s.length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Passed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s.filter(e=>e.passed).length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-red-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Failed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s.filter(e=>!e.passed).length})]})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"h-8 w-8 text-purple-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pass Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[s.length>0?Math.round(s.filter(e=>e.passed).length/s.length*100):0,"%"]})]})]})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Filters"}),(0,a.jsx)(n.BT,{children:"Filter assessments by type and search"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(o.p,{placeholder:"Search students, types...",value:T,onChange:e=>E(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(c.l6,{value:P,onValueChange:D,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"All Types"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"all",children:"All Types"}),(0,a.jsx)(c.eb,{value:"LEVEL_TEST",children:"Level Test"}),(0,a.jsx)(c.eb,{value:"PROGRESS_TEST",children:"Progress Test"}),(0,a.jsx)(c.eb,{value:"FINAL_EXAM",children:"Final Exam"}),(0,a.jsx)(c.eb,{value:"GROUP_TEST",children:"Group Test"})]})]}),(0,a.jsxs)(d.$,{onClick:z,variant:"outline",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Search"]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Assessment Records"}),(0,a.jsxs)(n.BT,{children:["Showing ",B.length," recorded test results"]})]}),(0,a.jsxs)(n.Wu,{children:[A?(0,a.jsx)("div",{className:"flex justify-center py-8",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)(u.XI,{children:[(0,a.jsx)(u.A0,{children:(0,a.jsxs)(u.Hj,{children:[(0,a.jsx)(u.nd,{children:"Student/Group"}),(0,a.jsx)(u.nd,{children:"Test Name"}),(0,a.jsx)(u.nd,{children:"Type"}),(0,a.jsx)(u.nd,{children:"Level"}),(0,a.jsx)(u.nd,{children:"Score"}),(0,a.jsx)(u.nd,{children:"Result"}),(0,a.jsx)(u.nd,{children:"Completed"})]})}),(0,a.jsx)(u.BF,{children:B.map(e=>(0,a.jsxs)(u.Hj,{children:[(0,a.jsx)(u.nA,{children:(0,a.jsx)("div",{children:e.student?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"font-medium",children:e.student.user.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.student.user.email})]}):e.group?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"font-medium",children:e.group.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.group.course.name})]}):(0,a.jsx)("span",{className:"text-gray-400",children:"-"})})}),(0,a.jsx)(u.nA,{children:(0,a.jsx)("div",{className:"font-medium",children:e.testName||"Legacy Assessment"})}),(0,a.jsx)(u.nA,{children:(0,a.jsx)(i.E,{className:V(e.type),children:e.type.replace("_"," ")})}),(0,a.jsx)(u.nA,{children:e.level||"-"}),(0,a.jsx)(u.nA,{children:void 0!==e.score&&e.maxScore?`${e.score}/${e.maxScore}`:"-"}),(0,a.jsx)(u.nA,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[H(e.passed),(0,a.jsx)("span",{children:e.passed?"Passed":"Failed"})]})}),(0,a.jsx)(u.nA,{children:e.completedAt?(0,p.Yq)(e.completedAt):"-"})]},e.id))})]})}),0===B.length&&!A&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(b.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No assessment records found."}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Start by recording test results for a student group."})]})]})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(h.A,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Only Test Administrators can access the Assessment system."})]})})}},26134:(e,s,t)=>{"use strict";t.d(s,{G$:()=>V,Hs:()=>v,UC:()=>et,VY:()=>er,ZL:()=>ee,bL:()=>K,bm:()=>el,hE:()=>ea,hJ:()=>es,l9:()=>Q});var a=t(43210),r=t(70569),l=t(98599),n=t(11273),d=t(96963),o=t(65551),i=t(31355),c=t(32547),m=t(25028),u=t(46059),x=t(14163),p=t(1359),f=t(42247),h=t(63376),j=t(8730),g=t(60687),b="Dialog",[N,v]=(0,n.A)(b),[y,w]=N(b),C=e=>{let{__scopeDialog:s,children:t,open:r,defaultOpen:l,onOpenChange:n,modal:i=!0}=e,c=a.useRef(null),m=a.useRef(null),[u,x]=(0,o.i)({prop:r,defaultProp:l??!1,onChange:n,caller:b});return(0,g.jsx)(y,{scope:s,triggerRef:c,contentRef:m,contentId:(0,d.B)(),titleId:(0,d.B)(),descriptionId:(0,d.B)(),open:u,onOpenChange:x,onOpenToggle:a.useCallback(()=>x(e=>!e),[x]),modal:i,children:t})};C.displayName=b;var R="DialogTrigger",A=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,n=w(R,t),d=(0,l.s)(s,n.triggerRef);return(0,g.jsx)(x.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":Z(n.open),...a,ref:d,onClick:(0,r.m)(e.onClick,n.onOpenToggle)})});A.displayName=R;var S="DialogPortal",[T,E]=N(S,{forceMount:void 0}),P=e=>{let{__scopeDialog:s,forceMount:t,children:r,container:l}=e,n=w(S,s);return(0,g.jsx)(T,{scope:s,forceMount:t,children:a.Children.map(r,e=>(0,g.jsx)(u.C,{present:t||n.open,children:(0,g.jsx)(m.Z,{asChild:!0,container:l,children:e})}))})};P.displayName=S;var D="DialogOverlay",k=a.forwardRef((e,s)=>{let t=E(D,e.__scopeDialog),{forceMount:a=t.forceMount,...r}=e,l=w(D,e.__scopeDialog);return l.modal?(0,g.jsx)(u.C,{present:a||l.open,children:(0,g.jsx)(F,{...r,ref:s})}):null});k.displayName=D;var _=(0,j.TL)("DialogOverlay.RemoveScroll"),F=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(D,t);return(0,g.jsx)(f.A,{as:_,allowPinchZoom:!0,shards:[r.contentRef],children:(0,g.jsx)(x.sG.div,{"data-state":Z(r.open),...a,ref:s,style:{pointerEvents:"auto",...a.style}})})}),I="DialogContent",L=a.forwardRef((e,s)=>{let t=E(I,e.__scopeDialog),{forceMount:a=t.forceMount,...r}=e,l=w(I,e.__scopeDialog);return(0,g.jsx)(u.C,{present:a||l.open,children:l.modal?(0,g.jsx)(O,{...r,ref:s}):(0,g.jsx)(G,{...r,ref:s})})});L.displayName=I;var O=a.forwardRef((e,s)=>{let t=w(I,e.__scopeDialog),n=a.useRef(null),d=(0,l.s)(s,t.contentRef,n);return a.useEffect(()=>{let e=n.current;if(e)return(0,h.Eq)(e)},[]),(0,g.jsx)(M,{...e,ref:d,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let s=e.detail.originalEvent,t=0===s.button&&!0===s.ctrlKey;(2===s.button||t)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=a.forwardRef((e,s)=>{let t=w(I,e.__scopeDialog),r=a.useRef(!1),l=a.useRef(!1);return(0,g.jsx)(M,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||t.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,"pointerdown"===s.detail.originalEvent.type&&(l.current=!0));let a=s.target;t.triggerRef.current?.contains(a)&&s.preventDefault(),"focusin"===s.detail.originalEvent.type&&l.current&&s.preventDefault()}})}),M=a.forwardRef((e,s)=>{let{__scopeDialog:t,trapFocus:r,onOpenAutoFocus:n,onCloseAutoFocus:d,...o}=e,m=w(I,t),u=a.useRef(null),x=(0,l.s)(s,u);return(0,p.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:n,onUnmountAutoFocus:d,children:(0,g.jsx)(i.qW,{role:"dialog",id:m.contentId,"aria-describedby":m.descriptionId,"aria-labelledby":m.titleId,"data-state":Z(m.open),...o,ref:x,onDismiss:()=>m.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(X,{titleId:m.titleId}),(0,g.jsx)(Y,{contentRef:u,descriptionId:m.descriptionId})]})]})}),q="DialogTitle",U=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(q,t);return(0,g.jsx)(x.sG.h2,{id:r.titleId,...a,ref:s})});U.displayName=q;var W="DialogDescription",$=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,r=w(W,t);return(0,g.jsx)(x.sG.p,{id:r.descriptionId,...a,ref:s})});$.displayName=W;var z="DialogClose",B=a.forwardRef((e,s)=>{let{__scopeDialog:t,...a}=e,l=w(z,t);return(0,g.jsx)(x.sG.button,{type:"button",...a,ref:s,onClick:(0,r.m)(e.onClick,()=>l.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}B.displayName=z;var J="DialogTitleWarning",[V,H]=(0,n.q)(J,{contentName:I,titleName:q,docsSlug:"dialog"}),X=({titleId:e})=>{let s=H(J),t=`\`${s.contentName}\` requires a \`${s.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${s.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${s.docsSlug}`;return a.useEffect(()=>{e&&(document.getElementById(e)||console.error(t))},[t,e]),null},Y=({contentRef:e,descriptionId:s})=>{let t=H("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${t.contentName}}.`;return a.useEffect(()=>{let t=e.current?.getAttribute("aria-describedby");s&&t&&(document.getElementById(s)||console.warn(r))},[r,e,s]),null},K=C,Q=A,ee=P,es=k,et=L,ea=U,er=$,el=B},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37826:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>u,L3:()=>p,c7:()=>x,lG:()=>o,rr:()=>f,zM:()=>i});var a=t(60687),r=t(43210),l=t(26134),n=t(11860),d=t(96241);let o=l.bL,i=l.l9,c=l.ZL;l.bm;let m=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hJ,{ref:t,className:(0,d.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));m.displayName=l.hJ.displayName;let u=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(m,{}),(0,a.jsx)(l.UC,{ref:r,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",e),...t,children:(0,a.jsxs)("div",{className:"relative",children:[s,(0,a.jsxs)(l.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]}));u.displayName=l.UC.displayName;let x=({className:e,...s})=>(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});x.displayName="DialogHeader";let p=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hE,{ref:t,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));p.displayName=l.hE.displayName;let f=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.VY,{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",e),...s}));f.displayName=l.VY.displayName},39390:(e,s,t)=>{"use strict";t.d(s,{J:()=>i});var a=t(60687),r=t(43210),l=t(78148),n=t(24224),d=t(96241);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.b,{ref:t,className:(0,d.cn)(o(),e),...s}));i.displayName=l.b.displayName},55192:(e,s,t)=>{"use strict";t.d(s,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>d});var a=t(60687),r=t(43210),l=t(96241);let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));n.displayName="Card";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));i.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>h,gC:()=>f,l6:()=>c,yv:()=>m});var a=t(60687),r=t(43210),l=t(22670),n=t(78272),d=t(3589),o=t(13964),i=t(96241);let c=l.bL;l.YJ;let m=l.WT,u=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.l9,{ref:r,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=l.l9.displayName;let x=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.PP,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})}));x.displayName=l.PP.displayName;let p=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.wn,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));p.displayName=l.wn.displayName;let f=r.forwardRef(({className:e,children:s,position:t="popper",...r},n)=>(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:n,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,i.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(p,{})]})}));f.displayName=l.UC.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.JU,{ref:t,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let h=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.q7,{ref:r,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:s})]}));h.displayName=l.q7.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.wv.displayName},68988:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(60687),r=t(43210),l=t(96241);let n=r.forwardRef(({className:e,type:s,...t},r)=>(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));n.displayName="Input"},78148:(e,s,t)=>{"use strict";t.d(s,{b:()=>d});var a=t(43210),r=t(14163),l=t(60687),n=a.forwardRef((e,s)=>(0,l.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var d=n},78330:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\assessments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\assessments\\page.tsx","default")},83281:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(18962).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},95487:(e,s,t)=>{Promise.resolve().then(t.bind(t,78330))},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(18962).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96752:(e,s,t)=>{"use strict";t.d(s,{A0:()=>d,BF:()=>o,Hj:()=>i,XI:()=>n,nA:()=>m,nd:()=>c});var a=t(60687),r=t(43210),l=t(96241);let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",e),...s}));d.displayName="TableHeader";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...s}));o.displayName="TableBody",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));i.displayName="TableRow";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));c.displayName="TableHead";let m=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));m.displayName="TableCell",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4243,7615,2918,8887,8706,6631],()=>t(9547));module.exports=a})();