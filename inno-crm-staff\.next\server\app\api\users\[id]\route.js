"use strict";(()=>{var e={};e.id=6100,e.ids=[6100],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50781:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>R,serverHooks:()=>A,workAsyncStorage:()=>N,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{DELETE:()=>g,GET:()=>x,PUT:()=>m});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),u=t(19854),d=t(41098),l=t(79464),p=t(99326),c=t(45697),h=t(97110),w=t.n(h);let f=c.z.object({name:c.z.string().min(2,"Name must be at least 2 characters").optional(),phone:c.z.string().min(9,"Phone number must be at least 9 characters").optional(),email:c.z.string().email().optional(),role:c.z.enum(["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]).optional(),password:c.z.string().min(6,"Password must be at least 6 characters").optional()});async function x(e,{params:r}){try{let e=await (0,u.getServerSession)(d.N);if(!e?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r,s=await l.z.user.findUnique({where:{id:t},select:{id:!0,name:!0,phone:!0,email:!0,role:!0,createdAt:!0,updatedAt:!0,studentProfile:{select:{id:!0,level:!0,branch:!0,status:!0,emergencyContact:!0,dateOfBirth:!0,address:!0}},teacherProfile:{select:{id:!0,subject:!0,experience:!0,salary:!0,branch:!0}},_count:{select:{activityLogs:!0}}}});if(!s)return i.NextResponse.json({error:"User not found"},{status:404});return i.NextResponse.json(s)}catch(e){return console.error("Error fetching user:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let t=await (0,u.getServerSession)(d.N);if(!t?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER"].includes(t.user.role))return i.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,a=await e.json(),n=f.parse(a),o=await l.z.user.findUnique({where:{id:s}});if(!o)return i.NextResponse.json({error:"User not found"},{status:404});if(n.phone&&n.phone!==o.phone&&await l.z.user.findUnique({where:{phone:n.phone}}))return i.NextResponse.json({error:"User with this phone number already exists"},{status:400});if(n.email&&n.email!==o.email&&await l.z.user.findUnique({where:{email:n.email}}))return i.NextResponse.json({error:"User with this email already exists"},{status:400});let c={};n.name&&(c.name=n.name),n.phone&&(c.phone=n.phone),void 0!==n.email&&(c.email=n.email),n.role&&(c.role=n.role),n.password&&(c.password=await w().hash(n.password,12));let h=await l.z.user.update({where:{id:s},data:c,select:{id:!0,name:!0,phone:!0,email:!0,role:!0,createdAt:!0,updatedAt:!0,studentProfile:{select:{id:!0,level:!0,branch:!0}},teacherProfile:{select:{id:!0,subject:!0,experience:!0,branch:!0}}}});return await p._.log({userId:t.user.id,userRole:t.user.role,action:"UPDATE",resource:"user",resourceId:h.id,details:{changes:n,targetUserName:h.name,targetUserRole:h.role},ipAddress:p._.getIpAddress(e),userAgent:p._.getUserAgent(e)}),i.NextResponse.json(h)}catch(e){if(e instanceof c.z.ZodError)return i.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating user:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e,{params:r}){try{let t=await (0,u.getServerSession)(d.N);if(!t?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||"ADMIN"!==t.user.role)return i.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r;if(s===t.user.id)return i.NextResponse.json({error:"Cannot delete your own account"},{status:400});let a=await l.z.user.findUnique({where:{id:s},include:{studentProfile:!0,teacherProfile:!0}});if(!a)return i.NextResponse.json({error:"User not found"},{status:404});return await l.z.$transaction(async e=>{if(a.studentProfile){let r=a.studentProfile.id;await e.payment.deleteMany({where:{studentId:r}}),await e.enrollment.deleteMany({where:{studentId:r}}),await e.attendance.deleteMany({where:{studentId:r}}),await e.assessment.deleteMany({where:{studentId:r}}),await e.student.delete({where:{id:r}})}if(a.teacherProfile){let r=a.teacherProfile.id;await e.group.updateMany({where:{teacherId:r},data:{teacherId:null}}),await e.teacher.delete({where:{id:r}})}await e.activityLog.deleteMany({where:{userId:s}}),await e.callRecord.deleteMany({where:{userId:s}}),await e.user.delete({where:{id:s}})}),await p._.log({userId:t.user.id,userRole:t.user.role,action:"DELETE",resource:"user",resourceId:s,details:{deletedUserName:a.name,deletedUserRole:a.role,hadStudentProfile:!!a.studentProfile,hadTeacherProfile:!!a.teacherProfile},ipAddress:p._.getIpAddress(e),userAgent:p._.getUserAgent(e)}),i.NextResponse.json({message:"User deleted successfully"})}catch(e){return console.error("Error deleting user:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let R=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/users/[id]/route",pathname:"/api/users/[id]",filename:"route",bundlePath:"app/api/users/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\users\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:N,workUnitAsyncStorage:y,serverHooks:A}=R;function j(){return(0,o.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:y})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")},97110:e=>{e.exports=require("bcryptjs")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(50781));module.exports=s})();