(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4432:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>s});var n=t(60687),o=t(82136);function s({children:e}){return(0,n.jsx)(o.SessionProvider,{children:e})}},5106:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\providers\\auth-provider.tsx","AuthProvider")},5161:(e,r,t)=>{Promise.resolve().then(t.bind(t,5106)),Promise.resolve().then(t.bind(t,13910))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13910:(e,r,t)=>{"use strict";t.d(r,{QueryProvider:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\components\\providers\\query-provider.tsx","QueryProvider")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39107:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},56321:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>a});var n=t(65239),o=t(48088),s=t(88170),i=t.n(s),d=t(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(r,l);let a={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],c={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:a}})},58014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>l});var n=t(37413),o=t(61421),s=t.n(o),i=t(5106),d=t(13910);t(82704);let l={title:"Innovative Centre CRM",description:"Customer Relationship Management System for Innovative Centre"};function a({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:s().className,children:(0,n.jsx)(d.QueryProvider,{children:(0,n.jsx)(i.AuthProvider,{children:e})})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70313:(e,r,t)=>{Promise.resolve().then(t.bind(t,4432)),Promise.resolve().then(t.bind(t,80924))},79435:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},80924:(e,r,t)=>{"use strict";t.d(r,{QueryProvider:()=>d});var n=t(60687),o=t(92314),s=t(8693),i=t(43210);function d({children:e}){let[r]=(0,i.useState)(()=>new o.E({defaultOptions:{queries:{staleTime:6e4}}}));return(0,n.jsx)(s.Ht,{client:r,children:e})}},82704:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4243,7615],()=>t(56321));module.exports=n})();