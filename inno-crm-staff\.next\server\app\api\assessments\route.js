"use strict";(()=>{var e={};e.id=7999,e.ids=[7999],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},84794:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>q,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{GET:()=>x,POST:()=>h});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(19854),d=r(41098),p=r(79464),l=r(99326),c=r(45697);let m=c.z.object({studentId:c.z.string().optional(),groupId:c.z.string().optional(),testName:c.z.string().min(1,"Test name is required"),type:c.z.enum(["LEVEL_TEST","PROGRESS_TEST","FINAL_EXAM","GROUP_TEST"]),level:c.z.enum(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]).optional(),score:c.z.number().optional(),maxScore:c.z.number().optional(),passed:c.z.boolean().default(!1),questions:c.z.any().optional(),results:c.z.any().optional(),completedAt:c.z.string().optional()});async function x(e){try{let t,r=await (0,u.getServerSession)(d.N);if(!r?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),n=s.get("studentId"),o=s.get("type");parseInt(s.get("page")||"1"),parseInt(s.get("limit")||"10");let a=s.get("branch")||"main",l="main"===a?"Main Branch":"Branch",c={branch:l};n&&(c.studentId=n,c.student={id:n,branch:l}),o&&(c.type=o);try{t=await p.z.assessment.findMany({where:c,include:{student:{include:{user:{select:{id:!0,name:!0,email:!0}}}}},orderBy:{createdAt:"desc"}})}catch(r){let e={};o&&(e.type=o),t=await p.z.assessment.findMany({where:e,include:{student:{include:{user:{select:{id:!0,name:!0,email:!0}}}}},orderBy:{createdAt:"desc"}})}return i.NextResponse.json({assessments:t})}catch(e){return console.error("Error fetching assessments:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let t,r=await (0,u.getServerSession)(d.N);if(!r?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.role||!["ADMIN","MANAGER","TEACHER"].includes(r.user.role))return i.NextResponse.json({error:"Forbidden"},{status:403});let s=await e.json(),n=m.parse(s),o=s.branch||"main";if(n.studentId&&!await p.z.student.findUnique({where:{id:n.studentId,branch:o}}))return i.NextResponse.json({error:"Student not found in the specified branch"},{status:404});if(n.groupId&&!await p.z.group.findUnique({where:{id:n.groupId,branch:o}}))return i.NextResponse.json({error:"Group not found in the specified branch"},{status:404});let{groupId:a,testName:c,...x}=n;if(!n.studentId)return i.NextResponse.json({error:"Student ID is required"},{status:400});if(!c)return i.NextResponse.json({error:"Test name is required"},{status:400});try{t=await p.z.assessment.create({data:{studentId:n.studentId,testName:c,type:n.type,level:n.level,score:n.score,maxScore:n.maxScore,passed:n.passed,questions:n.questions,results:n.results,branch:o,...a&&{groupId:a},completedAt:n.completedAt?new Date(n.completedAt):null},include:{student:{include:{user:{select:{id:!0,name:!0,email:!0}}}}}})}catch(e){t=await p.z.assessment.create({data:{studentId:n.studentId,testName:c,type:n.type,level:n.level,score:n.score,maxScore:n.maxScore,passed:n.passed,questions:n.questions,results:n.results,branch:o,completedAt:n.completedAt?new Date(n.completedAt):null},include:{student:{include:{user:{select:{id:!0,name:!0,email:!0}}}}}})}return await l._.logAssessmentCompleted(r.user.id,r.user.role,t.id,{type:t.type,studentName:t.student?.user.name||"Unknown",score:t.score,passed:t.passed},e),i.NextResponse.json(t,{status:201})}catch(e){if(e instanceof c.z.ZodError)return i.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error creating assessment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/assessments/route",pathname:"/api/assessments",filename:"route",bundlePath:"app/api/assessments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\assessments\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:q}=f;function w(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,5697,3412,1971],()=>r(84794));module.exports=s})();