(()=>{var e={};e.id=9890,e.ids=[9890],e.modules={439:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1215:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40918:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},50063:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(37413),a=r(51358),n=r(99455),i=r(94592);r(439);var l=r(53148),d=r(40918);r(88804);var c=r(61227);let o=(0,r(18618).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);var x=r(1215),m=r(65516);function u(){let e=[{id:1,name:"English A2 Level Certificate",course:"General English A2",completionDate:"2023-08-15",grade:"Excellent",score:92,issueDate:"2023-08-20",certificateNumber:"INN-A2-2023-001",status:"issued"},{id:2,name:"English A1 Level Certificate",course:"General English A1",completionDate:"2023-04-10",grade:"Good",score:85,issueDate:"2023-04-15",certificateNumber:"INN-A1-2023-045",status:"issued"}],s=[{id:3,name:"English B1 Level Certificate",course:"General English B1",expectedCompletion:"2024-03-15",progress:65,status:"in_progress"}],r=[{id:4,name:"English B2 Level Certificate",course:"General English B2",expectedStart:"2024-04-01",status:"upcoming"}],u=e=>{switch(e.toLowerCase()){case"excellent":return"bg-green-100 text-green-800";case"good":return"bg-blue-100 text-blue-800";case"satisfactory":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},h=e=>{switch(e){case"issued":return"bg-green-100 text-green-800";case"in_progress":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Certificates"}),(0,t.jsx)("p",{className:"text-gray-600",children:"View and download your academic certificates"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Completed Certificates"}),(0,t.jsx)(c.A,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.length}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Certificates earned"})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"In Progress"}),(0,t.jsx)(l.A,{className:"h-4 w-4 text-blue-600"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:s.length}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently studying"})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:"Upcoming"}),(0,t.jsx)(d.A,{className:"h-4 w-4 text-gray-600"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:r.length}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Future courses"})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Completed Certificates"]}),(0,t.jsx)(a.BT,{children:"Your earned certificates available for download"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-green-50",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-full",children:(0,t.jsx)(c.A,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.course}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(d.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["Completed: ",e.completionDate]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(o,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["Score: ",e.score,"%"]})]})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Certificate #: ",e.certificateNumber]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.E,{className:u(e.grade),children:e.grade}),(0,t.jsxs)(i.$,{size:"sm",variant:"outline",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"View"]}),(0,t.jsxs)(i.$,{size:"sm",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Download"]})]})]},e.id))})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center",children:[(0,t.jsx)(l.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Certificates in Progress"]}),(0,t.jsx)(a.BT,{children:"Courses you're currently taking"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-blue-50",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full",children:(0,t.jsx)(l.A,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.course}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,t.jsx)(d.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["Expected completion: ",e.expectedCompletion]})]}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs mb-1",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Progress"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.progress,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${e.progress}%`}})})]})]})]}),(0,t.jsx)(n.E,{className:h(e.status),children:"In Progress"})]},e.id))})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 mr-2 text-gray-600"}),"Upcoming Certificates"]}),(0,t.jsx)(a.BT,{children:"Future courses in your learning path"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full",children:(0,t.jsx)(d.A,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.course}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,t.jsx)(d.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["Expected start: ",e.expectedStart]})]})]})]}),(0,t.jsx)(n.E,{className:h(e.status),children:"Upcoming"})]},e.id))})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsx)(a.ZB,{children:"Certificate Information"})}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 border-l-4 border-blue-400 rounded",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Digital Certificates"}),(0,t.jsx)("p",{className:"text-sm text-blue-800 mt-1",children:"All certificates are digitally signed and can be verified online using the certificate number."})]}),(0,t.jsxs)("div",{className:"p-4 bg-green-50 border-l-4 border-green-400 rounded",children:[(0,t.jsx)("h4",{className:"font-medium text-green-900",children:"International Recognition"}),(0,t.jsx)("p",{className:"text-sm text-green-800 mt-1",children:"Our certificates are recognized internationally and follow CEFR standards."})]}),(0,t.jsxs)("div",{className:"p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded",children:[(0,t.jsx)("h4",{className:"font-medium text-yellow-900",children:"Certificate Verification"}),(0,t.jsx)("p",{className:"text-sm text-yellow-800 mt-1",children:"Employers can verify your certificates online at verify.innovativecentre.uz"})]})]})})]})]})}},51358:(e,s,r)=>{"use strict";r.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>l});var t=r(37413),a=r(61120),n=r(66819);let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));i.displayName="Card";let l=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},61227:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65516:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},66819:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(75986),a=r(8974);function n(...e){return(0,a.QP)((0,t.$)(e))}},78335:()=>{},83779:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(65239),a=r(48088),n=r(88170),i=r.n(n),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["student",{children:["certificates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,50063)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\certificates\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\certificates\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/student/certificates/page",pathname:"/dashboard/student/certificates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88804:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18618).A)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},94592:(e,s,r)=>{"use strict";r.d(s,{$:()=>c});var t=r(37413),a=r(61120),n=r(70403),i=r(50662),l=r(66819);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:s,size:r,asChild:a=!1,...i},c)=>{let o=a?n.DX:"button";return(0,t.jsx)(o,{className:(0,l.cn)(d({variant:s,size:r,className:e})),ref:c,...i})});c.displayName="Button"},96487:()=>{},99455:(e,s,r)=>{"use strict";r.d(s,{E:()=>l});var t=r(37413);r(61120);var a=r(50662),n=r(66819);let i=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:s,...r}){return(0,t.jsx)("div",{className:(0,n.cn)(i({variant:s}),e),...r})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,7144,6631],()=>r(83779));module.exports=t})();