(()=>{var e={};e.id=1767,e.ids=[1767],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,t,r)=>{"use strict";function s(e){let t=e.headers.get("X-Inter-Server-Secret"),r=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!t||!s||t!==s)return!1;if(r){let e=parseInt(r),t=Date.now();if(isNaN(e)||t-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function n(e,t){try{let r="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!r)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${r}${t.endpoint}`,n={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let t=Date.now().toString(),r=o.getServerConfig(),s=`${r.serverType}-${t}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":r.serverType,"X-Request-ID":s,"X-Timestamp":t,"User-Agent":`${r.serverType}-server`}}(),...t.headers},a=await fetch(s,{method:t.method,headers:n,body:t.data?JSON.stringify(t.data):void 0}),i=await a.json();return{success:a.ok,data:i,status:a.status,error:a.ok?void 0:i.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}r.d(t,{LQ:()=>o,cU:()=>a,g2:()=>s});class a{static async authenticateUser(e,t){return n("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:t}})}static async getUserData(e){return n("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,t){return n("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:t}})}}class o{static logRequest(e,t,r,s){let n=new Date().toISOString(),a=process.env.SERVER_TYPE||"unknown";console.log(`[${n}] Inter-Server ${e.toUpperCase()}: ${t}`,{server:a,success:r,details:s})}static async healthCheck(e){try{return(await n(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41098:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var s=r(13581),n=r(7786);let a={providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let t=await n.cU.authenticateUser(e.phone,e.password);if(!t.success)return console.error("Authentication failed:",t.error),null;let r=t.data.user;if(!r)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(r.role))return console.error("User role not allowed on staff server:",r.role),null;return{id:r.id,phone:r.phone,name:r.name,email:r.email,role:r.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role||null),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role||null),e)},pages:{signIn:"/auth/signin"}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61340:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(19854),c=r(41098),l=r(79464);async function d(e,{params:t}){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{id:r}=await t;if("STUDENT"===e.user.role&&e.user.id!==r)return i.NextResponse.json({error:"Forbidden"},{status:403});e.user.role;let s=await l.z.student.findUnique({where:{userId:r},include:{user:{select:{name:!0,email:!0}},currentGroup:{include:{course:{select:{name:!0,level:!0}},classes:{where:{homework:{not:null}},orderBy:{date:"desc"},take:20}}},assessments:{where:{type:{in:["PROGRESS_TEST","FINAL_EXAM"]}},orderBy:{createdAt:"desc"},take:10}}});if(!s)return i.NextResponse.json({error:"Student not found"},{status:404});let n=[],a=[],o=[];if(s.currentGroup?.classes){for(let e of s.currentGroup.classes)if(e.homework){let t=new Date(e.date);t.setDate(t.getDate()+3);let r=t<new Date,o=t<new Date(Date.now()-6048e5),i={id:`hw-${e.id}`,title:`Homework: ${e.topic||"Class Assignment"}`,course:s.currentGroup.course.name,type:"homework",dueDate:t.toISOString(),description:e.homework,status:o?"completed":r?"overdue":"pending",submittedAt:o?new Date(t.getTime()-864e5).toISOString():null,grade:o?85:null,feedback:o?"Assignment completed successfully.":null};o?a.push(i):r?n.push({...i,status:"overdue"}):n.push(i)}}for(let e of s.assessments)e.completedAt&&a.push({id:e.id,title:e.testName,course:s.currentGroup?.course.name||"Assessment",type:"assessment",dueDate:e.completedAt,description:`${e.type.replace("_"," ").toLowerCase()} assessment`,status:"completed",submittedAt:e.completedAt,grade:e.score,maxGrade:e.maxScore,passed:e.passed});if(s.currentGroup)for(let e of(await l.z.class.findMany({where:{groupId:s.currentGroup.id,date:{gt:new Date}},orderBy:{date:"asc"},take:3}))){let t=new Date(e.date);t.setDate(t.getDate()+3),o.push({id:`upcoming-${e.id}`,title:`Assignment: ${e.topic||"Class Assignment"}`,course:s.currentGroup.course.name,type:"homework",dueDate:t.toISOString(),description:"Assignment details will be provided in class",status:"upcoming"})}n.sort((e,t)=>new Date(e.dueDate).getTime()-new Date(t.dueDate).getTime()),a.sort((e,t)=>new Date(t.submittedAt||t.dueDate).getTime()-new Date(e.submittedAt||e.dueDate).getTime()),o.sort((e,t)=>new Date(e.dueDate).getTime()-new Date(t.dueDate).getTime());let d={student:{name:s.user.name,email:s.user.email,level:s.level,branch:s.branch},currentGroup:s.currentGroup?{name:s.currentGroup.name,course:s.currentGroup.course.name,level:s.currentGroup.course.level}:null,pending:n,completed:a,upcoming:o,statistics:{totalAssignments:n.length+a.length,completedAssignments:a.length,pendingAssignments:n.filter(e=>"pending"===e.status).length,overdueAssignments:n.filter(e=>"overdue"===e.status).length,averageGrade:a.filter(e=>e.grade).length>0?a.filter(e=>e.grade).reduce((e,t)=>e+(t.grade||0),0)/a.filter(e=>e.grade).length:0,completionRate:n.length+a.length>0?a.length/(n.length+a.length)*100:0}};return i.NextResponse.json(d)}catch(e){return console.error("Error fetching student assignments:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/students/[id]/assignments/route",pathname:"/api/students/[id]/assignments",filename:"route",bundlePath:"app/api/students/[id]/assignments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm\\inno-crm-staff\\app\\api\\students\\[id]\\assignments\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:h}=p;function v(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,3412],()=>r(61340));module.exports=s})();