{"/_not-found/page": "app/_not-found/page.js", "/api/announcements/route": "app/api/announcements/route.js", "/api/assessments/[id]/route": "app/api/assessments/[id]/route.js", "/api/attendance/[id]/route": "app/api/attendance/[id]/route.js", "/api/assessments/route": "app/api/assessments/route.js", "/api/attendance/route": "app/api/attendance/route.js", "/api/cabinets/[id]/route": "app/api/cabinets/[id]/route.js", "/api/auth/verify/route": "app/api/auth/verify/route.js", "/api/cabinets/route": "app/api/cabinets/route.js", "/api/cabinets/[id]/schedules/route": "app/api/cabinets/[id]/schedules/route.js", "/api/classes/[id]/route": "app/api/classes/[id]/route.js", "/api/classes/route": "app/api/classes/route.js", "/api/communication/stats/route": "app/api/communication/stats/route.js", "/api/courses/[id]/route": "app/api/courses/[id]/route.js", "/api/enrollments/[id]/route": "app/api/enrollments/[id]/route.js", "/api/courses/route": "app/api/courses/route.js", "/api/enrollments/route": "app/api/enrollments/route.js", "/api/dashboard/stats/route": "app/api/dashboard/stats/route.js", "/api/groups/[id]/route": "app/api/groups/[id]/route.js", "/api/groups/route": "app/api/groups/route.js", "/api/health/route": "app/api/health/route.js", "/api/inter-server/health/route": "app/api/inter-server/health/route.js", "/api/inter-server/sync/route": "app/api/inter-server/sync/route.js", "/api/leads/[id]/route": "app/api/leads/[id]/route.js", "/api/leads/[id]/call/route": "app/api/leads/[id]/call/route.js", "/api/leads/[id]/archive/route": "app/api/leads/[id]/archive/route.js", "/api/leads/[id]/assign-group/route": "app/api/leads/[id]/assign-group/route.js", "/api/leads/route": "app/api/leads/route.js", "/api/messages/route": "app/api/messages/route.js", "/api/leads/cleanup/route": "app/api/leads/cleanup/route.js", "/api/notifications/route": "app/api/notifications/route.js", "/api/notifications/test/route": "app/api/notifications/test/route.js", "/api/students/[id]/assignments/route": "app/api/students/[id]/assignments/route.js", "/api/students/[id]/certificates/route": "app/api/students/[id]/certificates/route.js", "/api/students/[id]/payments/route": "app/api/students/[id]/payments/route.js", "/api/students/[id]/progress/route": "app/api/students/[id]/progress/route.js", "/api/inter-server/auth/validate/route": "app/api/inter-server/auth/validate/route.js", "/api/students/[id]/route": "app/api/students/[id]/route.js", "/api/students/[id]/status/route": "app/api/students/[id]/status/route.js", "/api/students/current/dashboard/route": "app/api/students/current/dashboard/route.js", "/api/students/current/progress/route": "app/api/students/current/progress/route.js", "/api/students/dropped/route": "app/api/students/dropped/route.js", "/api/teachers/[id]/kpis/route": "app/api/teachers/[id]/kpis/route.js", "/api/students/route": "app/api/students/route.js", "/api/teachers/[id]/route": "app/api/teachers/[id]/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/api/teachers/route": "app/api/teachers/route.js", "/api/users/route": "app/api/users/route.js", "/api/workflows/route": "app/api/workflows/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/auth/signin/page": "app/auth/signin/page.js", "/page": "app/page.js", "/(dashboard)/dashboard/assessments/page": "app/(dashboard)/dashboard/assessments/page.js", "/(dashboard)/dashboard/attendance/page": "app/(dashboard)/dashboard/attendance/page.js", "/(dashboard)/dashboard/cabinets/[id]/page": "app/(dashboard)/dashboard/cabinets/[id]/page.js", "/(dashboard)/dashboard/cabinets/page": "app/(dashboard)/dashboard/cabinets/page.js", "/(dashboard)/dashboard/communication/messages/page": "app/(dashboard)/dashboard/communication/messages/page.js", "/(dashboard)/dashboard/communication/announcements/page": "app/(dashboard)/dashboard/communication/announcements/page.js", "/(dashboard)/dashboard/courses/page": "app/(dashboard)/dashboard/courses/page.js", "/(dashboard)/dashboard/enrollments/page": "app/(dashboard)/dashboard/enrollments/page.js", "/(dashboard)/dashboard/groups/[id]/page": "app/(dashboard)/dashboard/groups/[id]/page.js", "/(dashboard)/dashboard/communication/page": "app/(dashboard)/dashboard/communication/page.js", "/(dashboard)/dashboard/leads/page": "app/(dashboard)/dashboard/leads/page.js", "/(dashboard)/dashboard/groups/page": "app/(dashboard)/dashboard/groups/page.js", "/(dashboard)/dashboard/settings/page": "app/(dashboard)/dashboard/settings/page.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js", "/(dashboard)/dashboard/student/page": "app/(dashboard)/dashboard/student/page.js", "/(dashboard)/dashboard/student/assignments/page": "app/(dashboard)/dashboard/student/assignments/page.js", "/(dashboard)/dashboard/student/certificates/page": "app/(dashboard)/dashboard/student/certificates/page.js", "/(dashboard)/dashboard/student/attendance/page": "app/(dashboard)/dashboard/student/attendance/page.js", "/(dashboard)/dashboard/student/progress/page": "app/(dashboard)/dashboard/student/progress/page.js", "/(dashboard)/dashboard/student/schedule/page": "app/(dashboard)/dashboard/student/schedule/page.js", "/(dashboard)/dashboard/students/[id]/page": "app/(dashboard)/dashboard/students/[id]/page.js", "/(dashboard)/dashboard/student/payments/page": "app/(dashboard)/dashboard/student/payments/page.js", "/(dashboard)/dashboard/students/page": "app/(dashboard)/dashboard/students/page.js", "/(dashboard)/dashboard/teachers/page": "app/(dashboard)/dashboard/teachers/page.js", "/(dashboard)/dashboard/teachers/[id]/page": "app/(dashboard)/dashboard/teachers/[id]/page.js", "/(dashboard)/dashboard/test-notifications/page": "app/(dashboard)/dashboard/test-notifications/page.js", "/(dashboard)/dashboard/unauthorized/page": "app/(dashboard)/dashboard/unauthorized/page.js", "/(dashboard)/dashboard/users/page": "app/(dashboard)/dashboard/users/page.js"}