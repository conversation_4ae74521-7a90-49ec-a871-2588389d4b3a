{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico|public).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "-pv0Wt-8aqMUwBreHubJ4", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ch1cDn+BG5IYPLdK2u8ZKmurL9LkvXtyk0kVrS8G9wk=", "__NEXT_PREVIEW_MODE_ID": "886621d8fc7880cef52cdad00a188772", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6d4203465883a2c495b1671ab3a851c1d3ced3695bf83f46a508725c3cee0323", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4f00a227229342f111caa60808524e6cdeedb6f7e921fb16e2ec8225855e0ee0"}}}, "functions": {}, "sortedMiddleware": ["/"]}